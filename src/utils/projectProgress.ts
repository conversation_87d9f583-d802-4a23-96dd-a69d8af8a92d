interface ProjectProgressResult {
  progress: number;
  statusText: string;
  isActive: boolean;
}

export function calculateProjectProgress(
  startDate: string,
  endDate: string
): ProjectProgressResult {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const current = new Date();

  const totalDays = Math.max(
    1,
    (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)
  );

  const passedDays = Math.max(
    0,
    (current.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)
  );

  const progress = Math.min(
    100,
    Math.max(0, Math.round((passedDays / totalDays) * 100))
  );

  const isActive = current >= start && current <= end;
  let statusText = '已结束';

  if (isActive) {
    statusText = '进行中';
  } else if (current < start) {
    statusText = '未开始';
  }

  return {
    progress,
    statusText,
    isActive,
  };
}