'use client';

import { Badge } from 'antd-mobile';
import clsx from 'clsx';
import {
  ChevronDown,
  ChevronRight,
  Edit3,
  GripVertical,
  Trash2,
} from 'lucide-react';
import type React from 'react';
import { useState } from 'react';
import type { TaskItem } from '../../types';

interface TaskCardProps {
  task: TaskItem;
  isDragging?: boolean;
  dragOverlay?: boolean;
  handle?: boolean;
  listeners?: Record<string, (event: Event) => void>;
  style?: React.CSSProperties;
  transform?: { x: number; y: number; scaleX?: number; scaleY?: number };
  transition?: string;
  onEdit?: (task: TaskItem) => void;
  onDelete?: (taskId: string) => void;
}

const statusColors = {
  未开始: 'bg-gray-100 text-gray-600',
  进行中: 'bg-blue-100 text-blue-600',
  已完成: 'bg-green-100 text-green-600',
};

const statusBadgeColors = {
  未开始: '#8B5CF6',
  进行中: '#3B82F6',
  已完成: '#10B981',
};

export const TaskCard: React.FC<TaskCardProps> = ({
  task,
  isDragging = false,
  dragOverlay = false,
  handle = false,
  listeners,
  style,
  transform,
  transition,
  onEdit,
  onDelete,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div
      className={clsx(
        'rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-300 ease-out',
        'hover:-translate-y-1 hover:border-gray-300 hover:shadow-lg',
        isDragging && 'rotate-2 scale-105 opacity-60 shadow-xl',
        dragOverlay &&
          'rotate-1 scale-110 border-blue-400 shadow-2xl ring-2 ring-blue-200'
      )}
      style={{
        ...style,
        transform: transform
          ? `translate3d(${Math.round(transform.x)}px, ${Math.round(transform.y)}px, 0) ${transform.scaleX ? `scaleX(${transform.scaleX})` : ''} ${transform.scaleY ? `scaleY(${transform.scaleY})` : ''}`
          : undefined,
        transition,
      }}
    >
      {/* 卡片头部 - 始终可见 */}
      <button
        className="flex w-full cursor-pointer items-center gap-2 p-4 text-left focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
        onClick={toggleExpanded}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            toggleExpanded();
          }
        }}
        type="button"
      >
        {/* 拖拽手柄 */}
        {handle && (
          <div
            className=" flex h-6 w-6 cursor-grab items-center justify-center text-gray-400 hover:text-gray-600 active:cursor-grabbing"
            {...listeners}
          >
            <GripVertical size={16} />
          </div>
        )}

        {/* 展开/折叠图标 */}
        <div className="flex h-6 w-6 items-center justify-center text-gray-500">
          {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
        </div>

        {/* 任务标题 */}
        <div className="min-w-0 flex-1">
          <h3 className="truncate font-medium text-base text-gray-900">
            {task.任务}
          </h3>
        </div>

        {/* 状态标签 */}
        <Badge
          className="flex-shrink-0 p-1"
          color={statusBadgeColors[task.状态]}
          content={task.状态}
        />
      </button>

      {/* 展开内容 */}
      {isExpanded && (
        <div className="slide-in-from-top-2 animate-in border-gray-100 border-t px-5 pb-5 duration-300">
          <div className="space-y-5 pt-5">
            {/* 任务目标 */}
            <div className="space-y-2">
              <h4 className="flex items-center font-semibold text-gray-800 text-sm">
                <div className="mr-2 h-1.5 w-1.5 rounded-full bg-blue-500" />
                任务目标
              </h4>
              <p className="pl-4 text-gray-700 text-sm leading-relaxed">
                {task.任务目标}
              </p>
            </div>

            {/* 说明 */}
            <div className="space-y-2">
              <h4 className="flex items-center font-semibold text-gray-800 text-sm">
                <div className="mr-2 h-1.5 w-1.5 rounded-full bg-green-500" />
                说明
              </h4>
              <p className="pl-4 text-gray-700 text-sm leading-relaxed">
                {task.说明}
              </p>
            </div>

            {/* 底部操作区域 */}
            <div className="flex items-center justify-between border-gray-100 border-t pt-4">
              <div className="flex items-center space-x-3">
                <div
                  className={clsx(
                    'rounded-full px-3 py-1.5 font-semibold text-xs shadow-sm',
                    statusColors[task.状态]
                  )}
                >
                  {task.状态}
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="font-medium text-gray-500 text-xs">
                  #{task.id}
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center gap-1">
                  {onEdit && (
                    <button
                      className="flex h-8 w-8 items-center justify-center rounded-lg text-gray-400 transition-all duration-200 hover:bg-blue-50 hover:text-blue-600"
                      onClick={(e) => {
                        e.stopPropagation();
                        onEdit(task);
                      }}
                      title="编辑任务"
                      type="button"
                    >
                      <Edit3 size={14} />
                    </button>
                  )}

                  {onDelete && (
                    <button
                      className="flex h-8 w-8 items-center justify-center rounded-lg text-gray-400 transition-all duration-200 hover:bg-red-50 hover:text-red-600"
                      onClick={(e) => {
                        e.stopPropagation();
                        onDelete(task.id);
                      }}
                      title="删除任务"
                      type="button"
                    >
                      <Trash2 size={14} />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
