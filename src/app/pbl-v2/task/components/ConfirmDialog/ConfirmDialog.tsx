'use client';

import { Dialog } from 'antd-mobile';
import { AlertTriangle } from 'lucide-react';
import type React from 'react';

interface ConfirmDialogProps {
  isOpen: boolean;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
  type?: 'warning' | 'danger';
}

export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  title,
  message,
  confirmText = '确认',
  cancelText = '取消',
  onConfirm,
  onCancel,
  type = 'warning',
}) => {
  return (
    <Dialog
      actions={[
        {
          key: 'cancel',
          text: cancelText,
          onClick: onCancel,
        },
        {
          key: 'confirm',
          text: confirmText,
          onClick: onConfirm,
          danger: type === 'danger',
          bold: true,
        },
      ]}
      content={
        <div className="px-4 py-6 text-center">
          <div className="mb-4 flex justify-center">
            <div
              className={`flex h-12 w-12 items-center justify-center rounded-full ${
                type === 'danger' ? 'bg-red-100' : 'bg-orange-100'
              }`}
            >
              <AlertTriangle
                className={
                  type === 'danger' ? 'text-red-600' : 'text-orange-600'
                }
                size={24}
              />
            </div>
          </div>

          <h3 className="mb-2 font-semibold text-gray-900 text-lg">{title}</h3>

          <p className="text-gray-600 text-sm leading-relaxed">{message}</p>
        </div>
      }
      onClose={onCancel}
      visible={isOpen}
    />
  );
};
