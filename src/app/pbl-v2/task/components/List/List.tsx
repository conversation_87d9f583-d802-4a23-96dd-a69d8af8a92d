import classNames from 'clsx';
import type React from 'react';
import { forwardRef } from 'react';

import styles from '../styles/dnd.module.scss';

export interface Props {
  children: React.ReactNode;
  columns?: number;
  style?: React.CSSProperties;
  horizontal?: boolean;
}

export const List = forwardRef<HTMLUListElement, Props>(
  ({ children, columns = 1, horizontal, style }: Props, ref) => {
    return (
      <ul
        className={classNames(styles.List, horizontal && styles.horizontal)}
        ref={ref}
        style={
          {
            ...style,
            '--columns': columns,
          } as React.CSSProperties
        }
      >
        {children}
      </ul>
    );
  }
);
