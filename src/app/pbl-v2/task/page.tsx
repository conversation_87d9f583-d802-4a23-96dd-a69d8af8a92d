'use client';

import { MeasuringStrategy } from '@dnd-kit/core';
import {
  type AnimateLayoutChanges,
  defaultAnimateLayoutChanges,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { FloatingBubble, Toast } from 'antd-mobile';
import { Plus } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import { ConfirmDialog } from './components/ConfirmDialog';
import { Sortable, type Props as SortableProps } from './components/Sortable';
import { TaskCard } from './components/TaskCard';
import { TaskForm } from './components/TaskForm';
import { mockTasks, type TaskItem } from './types';

export default function TaskPage() {
  // 状态管理
  const [tasks, setTasks] = useState<TaskItem[]>(mockTasks);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<TaskItem | undefined>();
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    taskId: string;
    taskName: string;
  }>({
    isOpen: false,
    taskId: '',
    taskName: '',
  });

  const animateLayoutChanges: AnimateLayoutChanges = (args) =>
    defaultAnimateLayoutChanges({ ...args, wasDragging: true });

  // 生成新的任务 ID
  const generateTaskId = useCallback(() => {
    return (
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15)
    );
  }, []);

  // 新增任务
  const handleAddTask = useCallback(() => {
    setEditingTask(undefined);
    setIsFormOpen(true);
  }, []);

  // 编辑任务
  const handleEditTask = useCallback((task: TaskItem) => {
    setEditingTask(task);
    setIsFormOpen(true);
  }, []);

  // 删除任务确认
  const handleDeleteTask = useCallback(
    (taskId: string) => {
      const task = tasks.find((t) => t.id === taskId);
      if (task) {
        setConfirmDialog({
          isOpen: true,
          taskId,
          taskName: task.任务,
        });
      }
    },
    [tasks]
  );

  // 确认删除任务
  const confirmDeleteTask = useCallback(() => {
    setTasks((prev) => prev.filter((t) => t.id !== confirmDialog.taskId));
    setConfirmDialog({ isOpen: false, taskId: '', taskName: '' });
    Toast.show({
      icon: 'success',
      content: '任务删除成功',
    });
  }, [confirmDialog.taskId]);

  // 取消删除
  const cancelDeleteTask = useCallback(() => {
    setConfirmDialog({ isOpen: false, taskId: '', taskName: '' });
  }, []);

  // 提交表单（新增或编辑）
  const handleFormSubmit = useCallback(
    (taskData: Omit<TaskItem, 'id'> & { id?: string }) => {
      console.log('🚀 ~ taskData:', taskData);
      if (editingTask) {
        // 编辑模式
        setTasks((prev) =>
          prev.map((t) =>
            t.id === editingTask.id
              ? ({ ...taskData, id: editingTask.id } as TaskItem)
              : t
          )
        );
      } else {
        // 新增模式
        const newTask: TaskItem = {
          ...taskData,
          id: generateTaskId(),
        } as TaskItem;
        console.log('🚀 ~ newTask:', newTask);
        setTasks((prev) => [newTask, ...prev]); // 头插，便于新增后立即看到
      }
      Toast.show({
        icon: 'success',
        content: editingTask ? '任务更新成功' : '任务创建成功',
      });
    },
    [editingTask, generateTaskId]
  );

  // 关闭表单
  const handleFormClose = useCallback(() => {
    setIsFormOpen(false);
    setEditingTask(undefined);
  }, []);

  // 统计
  const total = tasks.length;
  const done = useMemo(
    () => tasks.filter((t) => t.状态 === '已完成').length,
    [tasks]
  );

  // dnd 可见项 id（全部任务）
  const visibleIds = useMemo(() => tasks.map((t) => t.id), [tasks]);
  console.log('🚀 ~ visibleIds:', visibleIds);

  const renderTaskItem = ({
    dragOverlay,
    dragging,
    listeners,
    ref,
    style,
    transform,
    transition,
    value,
  }: {
    dragOverlay: boolean;
    dragging: boolean;
    listeners: Record<string, (event: Event) => void>;
    ref: React.Ref<HTMLElement>;
    style: React.CSSProperties | undefined;
    transform: {
      x: number;
      y: number;
      scaleX?: number;
      scaleY?: number;
    } | null;
    transition: string | null;
    value: string;
  }) => {
    const task = tasks.find((t) => t.id === value);
    if (!task) {
      return null;
    }

    return (
      <div ref={ref as React.LegacyRef<HTMLDivElement>} style={style}>
        <TaskCard
          dragOverlay={dragOverlay}
          handle={true}
          isDragging={dragging}
          listeners={listeners}
          onDelete={handleDeleteTask}
          onEdit={handleEditTask}
          task={task}
          transform={transform || undefined}
          transition={transition || undefined}
        />
      </div>
    );
  };

  const props: Partial<SortableProps> = {
    strategy: verticalListSortingStrategy,
    items: visibleIds,
    renderItem: renderTaskItem,
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto max-w-4xl px-4">
        <div className="mb-4 pt-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="mb-1 font-bold text-2xl text-gray-900">
                PBL 项目任务管理
              </h1>
              <p className="text-gray-600">
                拖拽任务卡片可以调整顺序，点击卡片展开详情；支持统计
              </p>
            </div>

            <div className="flex items-center gap-2 text-gray-500 text-sm">
              <span>共 {total} 个任务</span>
              <div className="h-4 w-px bg-gray-300" />
              <span>已完成 {done} 个</span>
            </div>
          </div>
        </div>

        {tasks.length > 0 ? (
          <div className="space-y-3 pb-20">
            <Sortable
              {...props}
              animateLayoutChanges={animateLayoutChanges}
              handle
              measuring={{ droppable: { strategy: MeasuringStrategy.Always } }}
            />
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-20">
            <div className="mb-4 text-6xl text-gray-400">📝</div>
            <h3 className="mb-2 font-medium text-gray-900 text-lg">暂无任务</h3>
            <p className="mb-6 text-gray-500 text-sm">
              点击右下角 + 按钮创建你的第一个任务
            </p>
          </div>
        )}
      </div>

      {/* 浮动新增按钮 */}
      <FloatingBubble
        onClick={handleAddTask}
        style={{
          '--initial-position-bottom': '24px',
          '--initial-position-right': '24px',
          '--edge-distance': '24px',
        }}
      >
        <Plus size={24} />
      </FloatingBubble>

      {/* 任务表单 */}
      <TaskForm
        isOpen={isFormOpen}
        onClose={handleFormClose}
        onSubmit={handleFormSubmit}
        task={editingTask}
      />

      {/* 删除确认对话框 */}
      <ConfirmDialog
        cancelText="取消"
        confirmText="删除"
        isOpen={confirmDialog.isOpen}
        message={`确定要删除任务"${confirmDialog.taskName}"吗？此操作无法撤销。`}
        onCancel={cancelDeleteTask}
        onConfirm={confirmDeleteTask}
        title="删除任务"
        type="danger"
      />
    </div>
  );
}
