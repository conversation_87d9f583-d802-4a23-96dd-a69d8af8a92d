'use client';
import {
  type Block,
  BlockNoteSchema,
  defaultBlockSpecs,
  filterSuggestionItems,
  insertOrUpdateBlock,
} from '@blocknote/core';
import '@blocknote/core/fonts/inter.css';
import { BlockNoteView } from '@blocknote/mantine';
import '@blocknote/mantine/style.css';
import {
  createReactBlockSpec,
  ExperimentalMobileFormattingToolbarController,
  getDefaultReactSlashMenuItems,
  SuggestionMenuController,
  useCreateBlockNote,
} from '@blocknote/react';
import { Button, Popup, Checkbox } from 'antd-mobile';
import { clsx } from 'clsx';
import { Calendar, Camera, FileText, User, X, Search } from 'lucide-react';
import { PiEye, PiNotePencil, PiTextT } from 'react-icons/pi';

import '../../pbl/detail/components/BlockNote/styles.css';
// @ts-expect-error
import { zh } from '@blocknote/core/locales';
import { useState, useEffect } from 'react';
import { getObservationList } from '@/api/pbl';

const mockData: Block[] = [
  {
    id: 'a7df1188-4a32-4b93-8b5d-94ff2cdedb43',
    type: 'heading',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
      level: 2,
    },
    content: [
      {
        type: 'text',
        text: '“春天的种子”项目报告',
        styles: {},
      },
    ],
    children: [],
  },
  {
    id: '3158cd80-9610-4d18-aa31-6bb5c2dd29e0',
    type: 'heading',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
      level: 3,
    },
    content: [
      {
        type: 'text',
        text: '一、 项目基本信息',
        styles: {
          bold: true,
        },
      },
    ],
    children: [],
  },
  {
    id: '72dcce8b-ad3a-425b-a0f8-0f4a54bbc1f2',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '项目名称：',
        styles: {
          bold: true,
        },
      },
      {
        type: 'text',
        text: " 春天的种子 (Spring's Seeds)",
        styles: {},
      },
    ],
    children: [],
  },
  {
    id: 'ec9950f5-9b11-4b0b-9f19-3994afb38751',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '项目周期：',
        styles: {
          bold: true,
        },
      },
      {
        type: 'text',
        text: ' 4周 (例如：2024年3月4日 - 2024年3月29日)',
        styles: {},
      },
    ],
    children: [],
  },
  {
    id: 'a3b3f04e-0f8b-4e8a-8193-c5653a7f2005',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '参与班级：',
        styles: {
          bold: true,
        },
      },
      {
        type: 'text',
        text: ' 大班 (K3 Level)',
        styles: {},
      },
    ],
    children: [],
  },
  {
    id: '09c11e6e-c42c-41a9-90c7-9b2798b0f27f',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '指导教师：',
        styles: {
          bold: true,
        },
      },
      {
        type: 'text',
        text: ' [填写教师姓名]',
        styles: {},
      },
    ],
    children: [],
  },
  {
    id: '61c36a23-ec8b-4315-94df-71e1ed3e383c',
    type: 'heading',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
      level: 3,
    },
    content: [
      {
        type: 'text',
        text: '二、 项目概述',
        styles: {
          bold: true,
        },
      },
    ],
    children: [],
  },
  {
    id: '6083679c-ea63-4090-818d-b33314ce9368',
    type: 'paragraph',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '1. 项目来源与驱动性问题',
        styles: {
          bold: true,
        },
      },
    ],
    children: [],
  },
  {
    id: 'a5ecf1f4-3550-48f5-ad7b-f7179dab139e',
    type: 'paragraph',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '春天到来，万物复苏，孩子们在户外活动时对发芽的小草和新开的花朵产生了浓厚的兴趣。“种子是怎么长大的？”“为什么有的种子能发芽，有的不能？”为了回应孩子们的好奇心，我们将这些零散的问题整合，并共同提炼出本次项目的核心驱动性问题：',
        styles: {},
      },
    ],
    children: [],
  },
  {
    id: '5f7ced94-4a01-4888-a5a6-9128becfa4fe',
    type: 'quote',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '我们如何才能成功地唤醒一颗沉睡的种子，并帮助它在春天里健康成长？',
        styles: {
          bold: true,
        },
      },
      {
        type: 'text',
        text: ' (How can we successfully awaken a sleeping seed and help it grow healthily in the spring?)',
        styles: {},
      },
    ],
    children: [],
  },
  {
    id: '7a65515f-0cfc-4ada-abac-2a6da0fb5093',
    type: 'paragraph',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '2. 学习目标',
        styles: {
          bold: true,
        },
      },
    ],
    children: [],
  },
  {
    id: '1eb6a964-ccb8-4e40-b50b-53f024a3f2dd',
    type: 'paragraph',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '通过本次项目，我们期望幼儿在以下领域获得发展：',
        styles: {},
      },
    ],
    children: [],
  },
  {
    id: '70676c43-fd7e-4b45-a4a8-823201ef7d10',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '科学探究 (Scientific Inquiry):',
        styles: {
          bold: true,
        },
      },
    ],
    children: [
      {
        id: '7a32093c-29ba-42e9-9a25-db00905b8ffc',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '了解种子的基本结构和种类，认识植物的生命周期。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: '0c6412ce-49b6-4b4e-b736-15959ba79322',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '通过亲手种植，探究种子发芽和生长所需的基本条件（阳光、水、土壤、空气）。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: '97caaad7-f8c5-47c2-a818-b32aa3c39fd3',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '学习使用简单的工具进行观察、记录和测量。',
            styles: {},
          },
        ],
        children: [],
      },
    ],
  },
  {
    id: '96d4d8d4-7af0-439a-b57f-a68d5113f049',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '数学与逻辑 (Math & Logic):',
        styles: {
          bold: true,
        },
      },
    ],
    children: [
      {
        id: 'ab3fae37-27f8-4d05-9fbe-2a6401858442',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '运用计数、分类、比较等方法处理收集到的种子和数据。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: '5c15f0be-8305-487b-81a6-11cd45400ac4',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '通过测量植物高度，感知数量与时间的变化关系。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: 'e4f76501-daaf-47df-bfd7-84ea38cf34ed',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '学习按时间顺序（播种、发芽、长叶）整理和讲述事件。',
            styles: {},
          },
        ],
        children: [],
      },
    ],
  },
  {
    id: 'ea9916ad-f36a-42c2-8f8f-8a92b8719649',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '语言与表达 (Language & Expression):',
        styles: {
          bold: true,
        },
      },
    ],
    children: [
      {
        id: 'd07997a6-e6fd-4366-a942-813e00394d1a',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '丰富与植物相关的词汇（如：播种、发芽、根、茎、叶）。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: 'aa22a3b2-2c86-4154-aac4-fdb97d784857',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '能够清晰地描述自己的观察发现，并与同伴和老师交流。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: 'b75d2be4-c063-411f-b58b-8c1e314690e1',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '学习用连贯的语言讲述“一颗种子的旅行”故事。',
            styles: {},
          },
        ],
        children: [],
      },
    ],
  },
  {
    id: '8be8d16f-eb48-4e30-a035-fc7b23d11bfa',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '艺术与创造 (Art & Creativity):',
        styles: {
          bold: true,
        },
      },
    ],
    children: [
      {
        id: 'da410ef2-83e0-478e-bc07-a6a4d76c0450',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '运用绘画、手工、泥塑等多种形式表现种子的形态和植物的生长过程。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: '19c70b78-de2b-41ce-a87e-ae816cc8ca79',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '通过音乐和肢体动作，模仿种子的萌发和成长。',
            styles: {},
          },
        ],
        children: [],
      },
    ],
  },
  {
    id: '546268d0-58da-41b3-8a52-be4d653692ae',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '社会性与情感发展 (Social & Emotional Development):',
        styles: {
          bold: true,
        },
      },
    ],
    children: [
      {
        id: 'dacd3ea9-744c-41c9-8688-738e9d91d9a4',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '在小组合作中学会分工、协商，共同解决问题。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: 'ac7c7072-ad04-4b05-bdb8-2451c9309fe0',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '在照料植物的过程中，培养责任感、耐心和对生命的敬畏之心。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: 'eaef160c-cdd4-484e-aff9-9ad785f157fe',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '体验从播种到收获的喜悦，建立自信心和成就感。',
            styles: {},
          },
        ],
        children: [],
      },
    ],
  },
  {
    id: 'c8773f6a-aef0-4021-85e3-0929e61440b7',
    type: 'heading',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
      level: 3,
    },
    content: [
      {
        type: 'text',
        text: '三、 项目过程与活动',
        styles: {
          bold: true,
        },
      },
    ],
    children: [],
  },
  {
    id: '93a63cbf-7613-46dc-938e-6a972838a7ed',
    type: 'paragraph',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '第一周：启动与探索 (Launch & Exploration)',
        styles: {
          bold: true,
        },
      },
    ],
    children: [],
  },
  {
    id: '83802426-a497-47a3-9dc3-9cd8c89bcbcd',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '活动1：寻找春天的“信使”',
        styles: {
          bold: true,
        },
      },
    ],
    children: [
      {
        id: '3142626d-b5ca-44c1-a93f-84582aae1f03',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '描述：',
            styles: {
              bold: true,
            },
          },
          {
            type: 'text',
            text: ' 教师带领幼儿在幼儿园的自然角、花园里寻找春天来临的迹象，收集各种天然的种子（草籽、花籽、树下的果实等）。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: 'dee33457-75ec-4459-bc6d-9f644f9944e6',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '讨论：',
            styles: {
              bold: true,
            },
          },
          {
            type: 'text',
            text: ' 回到教室后，孩子们分享自己的发现，讨论“种子是什么？”“它们从哪里来？”“它们能做什么？”。教师通过绘本《小种子》引发更深入的思考。',
            styles: {},
          },
        ],
        children: [],
      },
    ],
  },
  {
    id: 'd02cf327-009d-446c-9b91-0f547dff8000',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '活动2：建立“种子博物馆”',
        styles: {
          bold: true,
        },
      },
    ],
    children: [
      {
        id: '674dfb2d-88f8-43c0-a8a1-3d8ae3efcc16',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '描述：',
            styles: {
              bold: true,
            },
          },
          {
            type: 'text',
            text: ' 孩子们将收集来的种子和家里带来的各种食用豆类进行分类、装饰和展览，并为它们制作标签。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: '0bcf9e4d-fe52-4c9e-9854-21232afcd544',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '成果：',
            styles: {
              bold: true,
            },
          },
          {
            type: 'text',
            text: ' 班级自然角升级为“种子博物馆”，孩子们初步建立了对种子多样性的认知。',
            styles: {},
          },
        ],
        children: [],
      },
    ],
  },
  {
    id: '336ef854-c0c6-462d-bb16-788eb35a0ef5',
    type: 'paragraph',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '第二周：调查与研究 (Investigation & Research)',
        styles: {
          bold: true,
        },
      },
    ],
    children: [],
  },
  {
    id: 'fd27567a-d1a5-4821-bc93-d5362dae187f',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '活动1：种植我们的种子',
        styles: {
          bold: true,
        },
      },
    ],
    children: [
      {
        id: '6a069f70-5586-464b-b5f6-9e39e36be873',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '描述：',
            styles: {
              bold: true,
            },
          },
          {
            type: 'text',
            text: ' 提供绿豆、黄豆、向日葵等易于观察的种子，让幼儿分组进行种植。每个小组需要自己准备花盆、装土、播种和浇水。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: '20cbe0fe-550b-4aea-bb77-a1111f62c27b',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '工具：',
            styles: {
              bold: true,
            },
          },
          {
            type: 'text',
            text: ' 制作“种子观察日记”，每天用绘画和符号记录种子的变化。',
            styles: {},
          },
        ],
        children: [],
      },
    ],
  },
  {
    id: '9ae32e8a-38ad-4f4f-a778-9a7d9a5f9b8a',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '活动2：种子发芽的秘密',
        styles: {
          bold: true,
        },
      },
    ],
    children: [
      {
        id: '855841ef-c4ce-4b17-9374-dd749dc74a0f',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '描述：',
            styles: {
              bold: true,
            },
          },
          {
            type: 'text',
            text: ' 设计对比实验。将种子分别放在“有水/无水”、“有阳光/无阳光”、“有土壤/无土壤”的环境中，观察并记录哪组种子能最先发芽。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: 'e9e04840-3956-49cd-87b0-ab58ed677f4c',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '发现：',
            styles: {
              bold: true,
            },
          },
          {
            type: 'text',
            text: ' 幼儿通过亲身实践，自主得出“种子发芽需要水、阳光和土壤”的结论。',
            styles: {},
          },
        ],
        children: [],
      },
    ],
  },
  {
    id: '5514a536-ce16-4a5e-9c6e-e4ce607a1ddf',
    type: 'paragraph',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '第三周：创作与建构 (Creation & Construction)',
        styles: {
          bold: true,
        },
      },
    ],
    children: [],
  },
  {
    id: 'b409465f-f54e-439d-9de7-3d92c2842243',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '活动1：艺术工作坊——种子的生命力',
        styles: {
          bold: true,
        },
      },
    ],
    children: [
      {
        id: '43f62b28-9459-4d40-8304-77ae8bb8b05e',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '描述：',
            styles: {
              bold: true,
            },
          },
          {
            type: 'text',
            text: ' 孩子们用超轻粘土制作种子发芽的模型，用颜料和画笔画出自己正在生长的植物，还用身体动作创编“种子破土而出”的舞蹈。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: '675ba7b5-8f7e-440a-a94e-a0ff63d573c9',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '成果：',
            styles: {
              bold: true,
            },
          },
          {
            type: 'text',
            text: ' 一系列充满想象力的艺术作品，展现了孩子们眼中种子的生命力。',
            styles: {},
          },
        ],
        children: [],
      },
    ],
  },
  {
    id: 'd88bff47-4227-4e0c-9cb8-425b5b8b5ad3',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '活动2：我是“护植小卫士”',
        styles: {
          bold: true,
        },
      },
    ],
    children: [
      {
        id: 'f87827ef-f08c-4ca4-bf59-3fc63a37002e',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '描述：',
            styles: {
              bold: true,
            },
          },
          {
            type: 'text',
            text: ' 随着小苗长高，孩子们开始担心虫子、缺水等问题。大家一起讨论如何保护小苗，并制作了“请爱护我”的标牌，学习为植物浇水、松土。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: '74ee89e7-875e-4ebf-8f50-1e2a9d2bc655',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '发展：',
            styles: {
              bold: true,
            },
          },
          {
            type: 'text',
            text: ' 责任感和解决问题的能力得到提升。',
            styles: {},
          },
        ],
        children: [],
      },
    ],
  },
  {
    id: '95d6c577-a092-40fc-af19-00617043a4fd',
    type: 'paragraph',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '第四周：展示与分享 (Presentation & Sharing)',
        styles: {
          bold: true,
        },
      },
    ],
    children: [],
  },
  {
    id: '716e15c8-7470-4cf3-93b6-0afc8091d2b3',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '活动1：“我们的迷你花园”成果展',
        styles: {
          bold: true,
        },
      },
    ],
    children: [
      {
        id: '2d5b9e53-1ec3-43bb-8f54-92cfe09be21b',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '描述：',
            styles: {
              bold: true,
            },
          },
          {
            type: 'text',
            text: ' 邀请家长和小班的弟弟妹妹们来参观我们的“迷你花园”。每个小组的成员作为“小小讲解员”，向参观者介绍自己种植的植物，并展示他们的“种子观察日记”。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: '7d74a1ec-97d6-40e8-a64f-3a96d09a4432',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '形式：',
            styles: {
              bold: true,
            },
          },
          {
            type: 'text',
            text: ' 结合了作品展示、口语表达和互动交流。',
            styles: {},
          },
        ],
        children: [],
      },
    ],
  },
  {
    id: '8b55fea9-b4b7-4010-8c5a-080d07a4108e',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '活动2：项目总结与庆祝',
        styles: {
          bold: true,
        },
      },
    ],
    children: [
      {
        id: '0d24f7e1-1b3e-4be9-ac1f-2ecc0f987a5f',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '描述：',
            styles: {
              bold: true,
            },
          },
          {
            type: 'text',
            text: ' 孩子们分享在项目中最有趣、最有挑战性的事。教师和孩子们一起回顾整个过程，并品尝用自己种出的豆苗制作的沙拉。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: 'cf8e5c87-8f3e-4e2b-9485-de94b73366e9',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '反思：',
            styles: {
              bold: true,
            },
          },
          {
            type: 'text',
            text: ' “原来我们吃的豆苗就是从那么小的豆子里长出来的！”孩子们将学习与生活紧密联系。',
            styles: {},
          },
        ],
        children: [],
      },
    ],
  },
  {
    id: '5ed553be-2358-4ab4-9e97-9664f604b245',
    type: 'heading',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
      level: 3,
    },
    content: [
      {
        type: 'text',
        text: '四、 项目成果',
        styles: {
          bold: true,
        },
      },
    ],
    children: [],
  },
  {
    id: '462b60bb-af67-49b8-9555-3c52a26e477c',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '实体成果：',
        styles: {
          bold: true,
        },
      },
    ],
    children: [
      {
        id: 'bdbfc4ce-9008-4810-ae12-87e4074a8aae',
        type: 'numberedListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '班级“种子博物馆”一个。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: '501f8915-003e-46ad-941f-ac275f8342cd',
        type: 'numberedListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '每组一份成功种植的盆栽植物。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: '205d52e6-2971-4575-a8d4-8082a8417cdc',
        type: 'numberedListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '每人一本图文并茂的《种子观察日记》。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: '7270f25c-bf74-4310-bad7-e4418571a573',
        type: 'numberedListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '关于种子和植物的个人及集体艺术作品（绘画、手工作品等）。',
            styles: {},
          },
        ],
        children: [],
      },
    ],
  },
  {
    id: '15c2a13b-1dac-4fb7-afff-ae4073167d4c',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '经验成果：',
        styles: {
          bold: true,
        },
      },
    ],
    children: [
      {
        id: 'd85b7568-866d-46d8-aafc-0f71b3caca17',
        type: 'numberedListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '幼儿获得了关于种子和植物生长的第一手科学知识。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: '9a158b12-9bf9-4452-8ddc-c1362b8b502c',
        type: 'numberedListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '幼儿的观察、记录、合作、表达和创造能力得到了显著提升。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: '98a86eb5-594f-45b6-9e49-b77e32fa7e02',
        type: 'numberedListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
          start: 1,
        },
        content: [
          {
            type: 'text',
            text: '幼儿建立了初步的责任感和对生命的热爱。',
            styles: {},
          },
        ],
        children: [],
      },
    ],
  },
  {
    id: '9baa177f-2691-4074-8e99-fbf505199337',
    type: 'heading',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
      level: 3,
    },
    content: [
      {
        type: 'text',
        text: '五、 项目评估',
        styles: {
          bold: true,
        },
      },
    ],
    children: [],
  },
  {
    id: '25c70223-26ac-4ff5-b2f3-c950a2b93c3b',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '过程性评估：',
        styles: {
          bold: true,
        },
      },
    ],
    children: [
      {
        id: '3b0a3151-4637-40da-9ca0-ccc69700630b',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '通过观察幼儿在小组活动中的参与度和协作情况。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: 'd479c420-bde2-436c-b60c-59f6a09ab55f',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '通过每日检查和点评幼儿的“种子观察日记”。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: '04cde7b5-6cb7-4a5a-9052-adeb31fce14e',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '通过记录幼儿在讨论环节中有意义的发言和提问。',
            styles: {},
          },
        ],
        children: [],
      },
    ],
  },
  {
    id: '9d04a8e8-3ffc-49e3-a1d0-1317585d5bd5',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '总结性评估：',
        styles: {
          bold: true,
        },
      },
    ],
    children: [
      {
        id: 'eaf95f24-7c79-429d-91a9-e0d140a27172',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '在最终成果展上，评估幼儿作为“小小讲解员”的表达能力和对知识的理解程度。',
            styles: {},
          },
        ],
        children: [],
      },
      {
        id: 'fe40d2e1-8b0e-4e3f-ae08-495df7333da1',
        type: 'bulletListItem',
        props: {
          textColor: 'default',
          backgroundColor: 'default',
          textAlignment: 'left',
        },
        content: [
          {
            type: 'text',
            text: '根据幼儿的最终作品和观察日记的完整性，进行综合评价。',
            styles: {},
          },
        ],
        children: [],
      },
    ],
  },
  {
    id: '74f7a920-b336-44a3-ac75-142d224b6ea5',
    type: 'heading',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
      level: 3,
    },
    content: [
      {
        type: 'text',
        text: '六、 教师反思',
        styles: {
          bold: true,
        },
      },
    ],
    children: [],
  },
  {
    id: 'a7cb6968-9597-48a7-ad60-d2b820bfb2ec',
    type: 'paragraph',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '本次“春天的种子”PBL项目取得了圆满成功。孩子们对探究活动始终保持着高昂的热情，驱动性问题贯穿始终。在整个过程中，幼儿的主体性得到了充分的发挥，他们不仅是知识的接受者，更是主动的探索者和建构者。',
        styles: {},
      },
    ],
    children: [],
  },
  {
    id: '5e40b0d6-0f6a-4793-b97d-3e0ed216bea4',
    type: 'paragraph',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '亮点与成功之处：',
        styles: {
          bold: true,
        },
      },
    ],
    children: [],
  },
  {
    id: '97151884-9937-4164-85c6-51299fca1c49',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '实验探究环节的设计非常成功，孩子们通过对比实验自主发现了科学结论，这比单纯的讲授更深刻。',
        styles: {},
      },
    ],
    children: [],
  },
  {
    id: '8d43c971-7d2b-4232-a5f3-18200d82e679',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '“种子观察日记”是一个极佳的支架，它帮助孩子们将持续的观察转化为可见的成果。',
        styles: {},
      },
    ],
    children: [],
  },
  {
    id: '84ed4b6a-7a39-4d0d-9086-54596067a810',
    type: 'paragraph',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '不足与改进方向：',
        styles: {
          bold: true,
        },
      },
    ],
    children: [],
  },
  {
    id: '03c4f878-3d0a-4180-bdd9-c09779357552',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '可以引入更多不同类型的种子，例如一些生长周期较长或形态特殊的种子，以增加挑战和趣味性。',
        styles: {},
      },
    ],
    children: [],
  },
  {
    id: '1882c5b1-211f-445a-9c21-b80e917d6908',
    type: 'bulletListItem',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [
      {
        type: 'text',
        text: '未来可以尝试将项目延伸到户外，开辟一小块真实的菜地，让孩子们体验更完整的耕作过程。',
        styles: {},
      },
    ],
    children: [],
  },
  {
    id: '6ac301e5-cde7-457c-9c59-83e31a5de88f',
    type: 'paragraph',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [],
    children: [],
  },
];

// 模拟观察记录数据
const mockObservationData = [
  {
    id: 1,
    date: '2024年3月15日',
    time: '09:30',
    observer: {
      name: '李老师',
      avatar:
        'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
    },
    student: '小明',
    activity: '种子观察',
    content:
      '小明今天非常仔细地观察绿豆的发芽情况，用放大镜仔细查看根部的变化，并主动记录在观察日记中。他发现根部比昨天长了约2毫米，表现出很强的观察能力和科学探究精神。',
    images: [
      'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?w=300&h=200&fit=crop',
    ],
    skills: ['观察能力', '记录能力', '科学探究'],
    conclusion: '小明在本次观察活动中表现优秀，具备良好的科学素养和探究精神。',
  },
  {
    id: 2,
    date: '2024年3月16日',
    time: '14:20',
    observer: {
      name: '王老师',
      avatar:
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
    },
    student: '小红',
    activity: '植物护理',
    content:
      '小红主动为班级的小苗浇水，动作轻柔，水量控制得当。她还提醒其他小朋友不要碰触嫩芽，表现出很强的责任心和爱护植物的意识。在小组讨论中积极分享自己的观察发现。',
    images: [
      'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
    ],
    skills: ['责任心', '爱心', '团队合作'],
    conclusion: '小红展现了良好的责任感和团队协作能力，是班级的好榜样。',
  },
  {
    id: 3,
    date: '2024年3月17日',
    time: '10:45',
    observer: {
      name: '张老师',
      avatar:
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
    },
    student: '小刚',
    activity: '实验探究',
    content:
      '小刚在对比实验中表现出色，能够清楚地描述有水和无水环境下种子的不同状态。他提出了"为什么有些种子发芽快，有些慢"的问题，并尝试用自己的方式寻找答案。逻辑思维能力较强。',
    images: [
      'https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=200&fit=crop',
    ],
    skills: ['逻辑思维', '问题意识', '实验能力'],
    conclusion: '小刚具备良好的科学思维和探究能力，建议继续培养其问题意识。',
  },
];

// 观察记录自定义块
export const ObservationRecord = createReactBlockSpec(
  {
    type: 'observationRecord',
    propSchema: {
      recordId: {
        default: 1,
      },
    },
    content: 'none',
  },
  {
    render: () => {
      const recordId = 1; // 默认使用第一条记录
      const record = mockObservationData.find((r) => r.id === recordId);

      if (!record) {
        return <div>观察记录未找到</div>;
      }

      return (
        <div className="my-6">
          {/* 观察内容 */}
          <div className="mb-4">
            <p className="text-sm leading-relaxed">{record.content}</p>
          </div>

          {/* 图片展示 */}
          {record.images && record.images.length > 0 && (
            <div className="mb-4">
              <div className="flex gap-2 overflow-x-auto">
                {record.images.map((image, index) => (
                  <img
                    alt={`观察图片 ${index + 1}`}
                    className="h-20 w-20 flex-shrink-0 rounded-lg object-cover"
                    key={`image-${record.id}-${index}`}
                    src={image}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      );
    },
  }
);

// 观察记录选择器组件
interface ObservationRecordSelectorProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (selectedRecords: any[]) => void;
}

function ObservationRecordSelector({ visible, onClose, onConfirm }: ObservationRecordSelectorProps) {
  const [searchText, setSearchText] = useState('');
  const [selectedRecords, setSelectedRecords] = useState<any[]>([]);
  const [observationRecords, setObservationRecords] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      fetchObservationRecords();
    }
  }, [visible]);

  const fetchObservationRecords = async () => {
    setLoading(true);
    try {
      const response = await getObservationList({
        pageNum: 1,
        pageSize: 100,
      });
      const records = response?.data?.list || response?.list || mockObservationData;
      setObservationRecords(records);
    } catch (error) {
      console.error('获取观察记录失败:', error);
      setObservationRecords(mockObservationData);
    } finally {
      setLoading(false);
    }
  };

  const filteredRecords = observationRecords.filter(record => 
    !searchText || 
    record.content?.toLowerCase().includes(searchText.toLowerCase()) ||
    record.student?.toLowerCase().includes(searchText.toLowerCase()) ||
    record.activity?.toLowerCase().includes(searchText.toLowerCase())
  );

  const handleRecordToggle = (record: any) => {
    const isSelected = selectedRecords.some(r => r.id === record.id);
    if (isSelected) {
      setSelectedRecords(prev => prev.filter(r => r.id !== record.id));
    } else {
      setSelectedRecords(prev => [...prev, record]);
    }
  };

  const handleConfirm = () => {
    onConfirm(selectedRecords);
    setSelectedRecords([]);
    setSearchText('');
    onClose();
  };

  const handleClose = () => {
    setSelectedRecords([]);
    setSearchText('');
    onClose();
  };

  return (
    <Popup
      visible={visible}
      onMaskClick={handleClose}
      position="bottom"
      bodyStyle={{
        borderTopLeftRadius: '12px',
        borderTopRightRadius: '12px',
        height: '80vh',
        maxHeight: '90vh',
      }}
    >
      <div className="flex h-full flex-col">
        {/* 标题栏 */}
        <div className="relative flex flex-shrink-0 items-center justify-center p-4 pb-0">
          <h3 className="font-medium text-gray-800 text-lg">选择观察记录</h3>
          <button
            className="absolute right-4 rounded-full p-1 hover:bg-gray-100"
            onClick={handleClose}
            type="button"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* 搜索框 */}
        <div className="flex-shrink-0 px-4 py-4">
          <div className="flex items-center rounded-lg bg-gray-50 px-3 py-2">
            <Search className="mr-2 h-4 w-4 text-gray-400" />
            <input
              className="flex-1 bg-transparent text-gray-700 text-sm placeholder-gray-400 outline-none"
              onChange={(e) => setSearchText(e.target.value)}
              placeholder="搜索观察记录内容、学生姓名或活动名称"
              type="text"
              value={searchText}
            />
          </div>
        </div>

        {/* 观察记录列表 */}
        <div className="flex-1 overflow-y-auto px-4">
          {loading ? (
            <div className="py-8 text-center text-gray-400">加载中...</div>
          ) : filteredRecords.length === 0 ? (
            <div className="py-8 text-center text-gray-400">
              <p>未找到相关观察记录</p>
              <p className="mt-1 text-sm">尝试调整搜索关键词</p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredRecords.map((record) => {
                const isSelected = selectedRecords.some(r => r.id === record.id);
                return (
                  <div
                    key={record.id}
                    className={clsx(
                      'rounded-lg border p-3 transition-all cursor-pointer',
                      isSelected
                        ? 'border-indigo-500 bg-indigo-50'
                        : 'border-gray-200 bg-white hover:border-gray-300'
                    )}
                    onClick={() => handleRecordToggle(record)}
                  >
                    <div className="flex items-start gap-3">
                      <Checkbox
                        checked={isSelected}
                        onChange={() => handleRecordToggle(record)}
                        className="mt-1"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="flex items-center gap-2">
                            <img
                              src={record.observer?.avatar}
                              alt={record.observer?.name}
                              className="w-6 h-6 rounded-full"
                            />
                            <span className="text-sm text-gray-600">{record.observer?.name}</span>
                          </div>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-sm font-medium text-gray-900">{record.student}</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-500">{record.date} {record.time}</span>
                        </div>
                        <div className="mb-2">
                          <span className="inline-block px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                            {record.activity}
                          </span>
                        </div>
                        <p className="text-sm text-gray-700 line-clamp-2">{record.content}</p>
                        {record.images && record.images.length > 0 && (
                          <div className="mt-2 flex gap-1">
                            {record.images.slice(0, 3).map((image, index) => (
                              <div key={index} className="w-8 h-8 rounded overflow-hidden">
                                <img
                                  src={image}
                                  alt={`观察图片 ${index + 1}`}
                                  className="w-full h-full object-cover"
                                />
                              </div>
                            ))}
                            {record.images.length > 3 && (
                              <div className="w-8 h-8 rounded bg-gray-100 flex items-center justify-center">
                                <span className="text-xs text-gray-500">+{record.images.length - 3}</span>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* 已选择记录 */}
        {selectedRecords.length > 0 && (
          <div className="flex-shrink-0 border-t border-gray-100 px-4 py-3">
            <div className="text-sm text-gray-600 mb-2">
              已选择 {selectedRecords.length} 条观察记录
            </div>
            <div className="flex flex-wrap gap-2">
              {selectedRecords.map((record) => (
                <div
                  key={record.id}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-indigo-100 text-indigo-700 text-xs rounded-full"
                >
                  <span>{record.student} - {record.activity}</span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRecordToggle(record);
                    }}
                    className="ml-1 hover:bg-indigo-200 rounded-full p-0.5"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 底部按钮 */}
        <div className="flex flex-shrink-0 gap-3 p-4 pt-3">
          <button
            className="flex-1 rounded-full border border-gray-300 py-3 font-medium text-gray-700"
            onClick={handleClose}
            type="button"
          >
            取消
          </button>
          <button
            className={clsx(
              'flex-1 rounded-full py-3 font-medium',
              selectedRecords.length > 0
                ? 'bg-indigo-500 text-white'
                : 'bg-gray-200 text-gray-400 cursor-not-allowed'
            )}
            onClick={handleConfirm}
            disabled={selectedRecords.length === 0}
            type="button"
          >
            插入 {selectedRecords.length > 0 ? `(${selectedRecords.length})` : ''}
          </button>
        </div>
      </div>
    </Popup>
  );
}

// 插入观察记录的函数
const insertObservationRecord = (editor: typeof schema.BlockNoteEditor, setModalVisible: (visible: boolean) => void) => ({
  title: '插入观察记录',
  onItemClick: () => {
    setModalVisible(true);
  },
  group: 'PBL 工具',
  icon: <PiEye />,
});
export const Custom = createReactBlockSpec(
  {
    type: 'custom',
    propSchema: {},
    content: 'none',
    deletable: false,
    draggable: false, // 禁止拖动
    isSelectable: false,
    readonly: true,
  },
  {
    render: () => {
      return (
        <div style={{ padding: '10px', backgroundColor: 'yellow' }}>
          Pretend this is a Custom block, and it contains a link
          <a
            href="https://google.com"
            rel="nofollow noreferrer noopener"
            target="_blank"
          >
            Google
          </a>
        </div>
      );
    },
  }
);

const insertAIPlaceholder = (editor: typeof schema.BlockNoteEditor) => ({
  title: 'AI 生成内容',
  onItemClick: () => {
    const insertedBlockId = insertOrUpdateBlock(editor, {
      type: 'custom',
      props: {
        textColor: 'default',
        backgroundColor: 'default',
        textAlignment: 'left',
      },
    });
    editor.setTextCursorPosition(insertedBlockId);
  },
  group: 'AI 工具',
  icon: <PiTextT />,
});

const { ...remainingBlockSpecs } = defaultBlockSpecs;

const schema = BlockNoteSchema.create({
  blockSpecs: {
    ...remainingBlockSpecs,
    // Adds the custom blocks
    custom: Custom,
    observationRecord: ObservationRecord,
  },
});

export default function App() {
  // Creates a new editor instance.
  const editor = useCreateBlockNote({
    schema,
    initialContent: mockData,
    dictionary: zh,
  });

  const [isEditable, setIsEditable] = useState(false);
  const [recordSelectorVisible, setRecordSelectorVisible] = useState(false);
  
  const onSave = () => {
    setIsEditable(false);
  };

  const handleInsertRecords = (selectedRecords: any[]) => {
    selectedRecords.forEach((record) => {
      const insertedBlockId = insertOrUpdateBlock(editor, {
        type: 'observationRecord',
        props: {
          recordId: record.id,
        },
      });
      editor.setTextCursorPosition(insertedBlockId);
    });
  };
  return (
    <div className={clsx('p-10', isEditable ? 'pl-12' : '')}>
      <BlockNoteView
        editable={isEditable}
        editor={editor}
        formattingToolbar={false}
        slashMenu={false}
      >
        <ExperimentalMobileFormattingToolbarController />
        <SuggestionMenuController
          getItems={async (query) =>
            filterSuggestionItems(
              [
                insertObservationRecord(editor, setRecordSelectorVisible),
                insertAIPlaceholder(editor),
                ...getDefaultReactSlashMenuItems(editor),
                // insertUserNotes(editor),
                // insertAIContent(editor)
              ],
              query
            )
          }
          triggerCharacter="/"
        />
      </BlockNoteView>
      <div className="fixed bottom-0 left-0 flex w-full items-center justify-center gap-2 p-4">
        <div className="">
          {isEditable ? (
            <Button
              block
              color="primary"
              onClick={onSave}
              shape="rounded"
              size="small"
            >
              退出编辑
            </Button>
          ) : (
            <Button
              block
              color="primary"
              onClick={() => setIsEditable(true)}
              shape="rounded"
              size="small"
            >
              <PiNotePencil />
              编辑报告
            </Button>
          )}
        </div>
      </div>
      
      {/* 观察记录选择器 */}
      <ObservationRecordSelector
        visible={recordSelectorVisible}
        onClose={() => setRecordSelectorVisible(false)}
        onConfirm={handleInsertRecords}
      />
    </div>
  );
}
