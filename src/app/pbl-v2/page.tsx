// biome-ignore lint/a11y/useSemanticElements: using semantic elements would require major refactor
'use client';

import { Dialog, Empty, Toast } from 'antd-mobile';
import { clsx } from 'clsx';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useMemo, useState } from 'react';
// 移除真实接口依赖，改为本地模拟
// import { deleteProject, getProjectList } from '@/api/pbl';
import { PiPlusCircle } from '@/components/Icons';
import ProjectCard from './components/ProjectCard';

import '../pbl/page.css';

interface Project {
  instId?: string;
  deptId?: string;
  projectId: string;
  projectName: string;
  description: string;
  theme?: string;
  drivingQuestion?: string;
  learningGoals?: string;
  keyword?: string;
  startDate: string;
  endDate: string;
  status?: number;
  delFlag?: number;
  createUserId?: string;
  createTime: string;
  updateTime: string;
  ownerName?: string;
  ownerAvatar?: string;
  cover?: string; // 新增封面字段
}

// 高拟真本地数据，用于展示
const MOCK_PROJECTS: Project[] = [
  {
    projectId: 'pbl-2401',
    projectName: '校园零碳倡议',
    description:
      '围绕“如何让校园更低碳”展开的跨学科项目，学生将通过能耗监测、行为设计与宣传落地，提出一套可执行的节能方案。',
    theme: '可持续发展',
    drivingQuestion: '如何在不牺牲舒适度的前提下降低校园能耗？',
    learningGoals: '数据调查、可视化表达、行为经济学基础、团队协作',
    keyword: '低碳, 节能, 行为设计',
    startDate: '2025-06-10',
    endDate: '2025-08-30',
    status: 1,
    createTime: '2025-03-01 09:12',
    updateTime: '2025-04-21 18:40',
    ownerName: '王若溪',
    ownerAvatar:
      'https://images.unsplash.com/photo-1544005313-94ddf0286df2?q=80&w=256&auto=format&fit=facearea&facepad=3&h=256',
    cover:
      'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?q=80&w=1200&auto=format&fit=crop&ixlib=rb-4.0.3',
  },
  {
    projectId: 'pbl-2398',
    projectName: '城市微气候观测',
    description:
      '以本校为样本，构建校园微气候观测网络，采集温湿度/风速/辐射数据，探索“热岛效应”在微尺度的表现与缓解策略。',
    theme: '城市与环境',
    drivingQuestion: '树荫、铺装与水体如何影响体感温度？',
    learningGoals: '实验设计、传感器搭建、GIS 制图、科研写作',
    keyword: '微气候, 热岛, 传感器',
    startDate: '2025-04-01',
    endDate: '2025-06-15',
    status: 0,
    createTime: '2025-03-26 14:05',
    updateTime: '2025-04-20 11:02',
    ownerName: '陈沉',
    ownerAvatar:
      'https://images.unsplash.com/photo-1502685104226-ee32379fefbe?q=80&w=256&auto=format&fit=facearea&facepad=3&h=256',
    cover:
      'https://images.unsplash.com/photo-1469474968028-56623f02e42e?q=80&w=1200&auto=format&fit=crop&ixlib=rb-4.0.3',
  },
  {
    projectId: 'pbl-2392',
    projectName: '传统手艺数字化守护',
    description:
      '以非遗手艺为对象，完成“口述史访谈 + 交互展示”，构建可被公众理解与传播的数字档案。',
    theme: '文化与科技',
    drivingQuestion: '年轻人为什么会远离传统手艺？如何用数字媒介重新讲述？',
    learningGoals: '采访方法、3D 建模、交互叙事、展览策划',
    keyword: '非遗, 口述史, 数字化',
    startDate: '2025-03-18',
    endDate: '2025-06-01',
    status: 1,
    createTime: '2025-03-10 10:20',
    updateTime: '2025-04-22 09:33',
    ownerName: 'Luna',
    ownerAvatar:
      'https://images.unsplash.com/photo-1520813792240-56fc4a3765a7?q=80&w=256&auto=format&fit=facearea&facepad=3&h=256',
    cover:
      'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?q=80&w=1200&auto=format&fit=crop&ixlib=rb-4.0.3',
  },
  {
    projectId: 'pbl-2386',
    projectName: '食物里的科学：从田间到餐桌',
    description:
      '溯源校园餐厅主要食材，分析碳足迹与营养构成，设计“更健康更低碳”的一周菜单并进行试吃评估。',
    theme: '食物系统',
    drivingQuestion: '价格、口味与低碳如何在菜单中取得平衡？',
    learningGoals: '营养学入门、生命周期评估、问卷与统计',
    keyword: '碳足迹, 菜单设计, 问卷',
    startDate: '2025-02-28',
    endDate: '2025-04-30',
    status: 2,
    createTime: '2025-02-20 16:11',
    updateTime: '2025-04-10 08:47',
    ownerName: '张至',
    ownerAvatar:
      'https://images.unsplash.com/photo-1547425260-76bcadfb4f2c?q=80&w=256&auto=format&fit=facearea&facepad=3&h=256',
    cover:
      'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?q=80&w=1200&auto=format&fit=crop&ixlib=rb-4.0.3',
  },
  {
    projectId: 'pbl-2380',
    projectName: '校园闲置物品循环计划',
    description:
      '为毕业季设计“闲置再流转”系统，包含回收、消毒、分类、二次上架以及可持续品牌传播。',
    theme: '社会创新',
    drivingQuestion: '如何让“舍不得丢”的物品有尊严地再被使用？',
    learningGoals: '服务设计、流程优化、品牌传播',
    keyword: '回收, 二手, 循环',
    startDate: '2025-04-05',
    endDate: '2025-06-20',
    status: 0,
    createTime: '2025-03-28 12:21',
    updateTime: '2025-04-22 10:06',
    ownerName: 'Iris',
    ownerAvatar:
      'https://images.unsplash.com/photo-1544005316-04ce2f2d6f9a?q=80&w=256&auto=format&fit=facearea&facepad=3&h=256',
    cover:
      'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?q=80&w=1200&auto=format&fit=crop&ixlib=rb-4.0.3',
  },
  {
    projectId: 'pbl-2374',
    projectName: '小小天文台 · 星空成像',
    description:
      '用树莓派搭建简易星空相机，记录城市光害差异并制作“可交互星图”。',
    theme: '科学与工程',
    drivingQuestion: '在城市里还能看见银河吗？',
    learningGoals: '硬件搭建、成像算法、数据可视化',
    keyword: '天文, 光害, 树莓派',
    startDate: '2025-03-02',
    endDate: '2025-05-15',
    status: 1,
    createTime: '2025-02-25 09:35',
    updateTime: '2025-04-19 21:15',
    ownerName: 'Leo',
    ownerAvatar:
      'https://images.unsplash.com/photo-1547425260-76bcadfb4f2c?q=80&w=256&auto=format&fit=facearea&facepad=3&h=256',
    cover:
      'https://images.unsplash.com/photo-1506197603052-3cc9c3a201bd?q=80&w=1200&auto=format&fit=crop&ixlib=rb-4.0.3',
  },
];

export default function PBLPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const router = useRouter();

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = 'PBL 项目';
    }
  }, []);

  // 模拟获取列表：首屏直接注入（如需骨架屏可改为异步并加入轻微延时）
  const fetchProjects = useCallback(() => {
    setProjects(MOCK_PROJECTS);
  }, []);

  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  // 本地删除：不调用接口，直接内存更新
  const handleDelete = async (projectId: string) => {
    const result = await Dialog.confirm({
      content: '确定要删除该项目吗？',
    });

    if (result) {
      try {
        setProjects((prev) => prev.filter((p) => p.projectId !== projectId));
        Toast.show({
          icon: 'success',
          content: '删除成功',
        });
      } catch (_error) {
        Toast.show({
          icon: 'fail',
          content: '删除失败',
        });
      }
    }
  };

  const hasProjects = useMemo(() => projects.length > 0, [projects.length]);

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-50 to-white">
      <div className="mx-auto max-w-7xl px-4 pt-6 pb-28 sm:px-6 lg:px-8">
        {hasProjects ? (
          <div className="grid grid-cols-1 gap-4 sm:gap-6 md:grid-cols-2 lg:grid-cols-3">
            {projects.map((project) => (
              <ProjectCard
                key={project.projectId}
                onDelete={handleDelete}
                project={project}
              />
            ))}
          </div>
        ) : (
          <div className="mt-10 rounded-2xl border border-slate-200 border-dashed bg-white/50 p-8 text-center shadow-sm backdrop-blur">
            <Empty description="暂无项目，点击下方按钮开始创建你的第一个 PBL 项目" />
          </div>
        )}

        {/* FAB 新建 */}
        <div className="pointer-events-none fixed inset-x-0 bottom-0 z-20 flex items-center justify-center pb-6">
          <button
            aria-label="新建项目"
            className={clsx(
              'pointer-events-auto inline-flex items-center justify-center gap-2 rounded-full px-5 py-3 text-white shadow-indigo-500/20 shadow-lg focus:outline-none focus-visible:ring-4 focus-visible:ring-indigo-300',
              'bg-gradient-to-r from-indigo-500 via-violet-500 to-purple-500 hover:from-indigo-600 hover:via-violet-600 hover:to-purple-600',
              'transition-all duration-300'
            )}
            onClick={() => router.push('/pbl-v2/create')}
            type="button"
          >
            <PiPlusCircle fontSize={22} />
            <span className="font-medium text-base">新建项目</span>
          </button>
        </div>
      </div>
    </div>
  );
}
