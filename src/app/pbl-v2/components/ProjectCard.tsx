'use client';

import { Card } from 'antd-mobile';
import { clsx } from 'clsx';
import { format } from 'date-fns';
import {
  Activity,
  Calendar,
  Edit,
  Eye,
  Send,
  Trash2,
  Users,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { PiDotsThreeOutlineFill } from 'react-icons/pi';
import { calculateProjectProgress } from '@/utils/projectProgress';
import PopoverMenu from '../../pbl/components/PopoverMenu';

interface Project {
  instId?: string;
  deptId?: string;
  projectId: string;
  projectName: string;
  description: string;
  theme?: string;
  drivingQuestion?: string;
  learningGoals?: string;
  keyword?: string;
  startDate: string;
  endDate: string;
  status?: number;
  delFlag?: number;
  createUserId?: string;
  createTime: string;
  updateTime: string;
  cover?: string; // 新增封面图，可选
}

interface ProjectCardProps {
  project: Project;
  onDelete?: (projectId: string) => void;
}

export default function ProjectCard({ project, onDelete }: ProjectCardProps) {
  const router = useRouter();
  const { progress, statusText, isActive } = calculateProjectProgress(
    project.startDate,
    project.endDate
  );

  const statusColorClass = (() => {
    if (isActive) {
      return 'border-emerald-200 bg-emerald-50 text-emerald-700';
    }
    if (statusText === '未开始') {
      return 'border-sky-200 bg-sky-50 text-sky-700';
    }
    return 'border-slate-200 bg-slate-50 text-slate-700';
  })();

  const statusCapsule = clsx(
    'absolute top-3 right-3 rounded-full border px-2.5 py-1 font-medium text-xs',
    statusColorClass
  );

  const handleView = () => {
    router.push('/pbl-v2/detail?projectId=1');
  };

  const handleEdit = () => {
    router.push(
      `/pbl/create?projectId=${encodeURIComponent(project.projectId)}`
    );
  };

  const handleRecord = () => {
    router.push(
      `/pbl/detail?projectId=${encodeURIComponent(project.projectId)}&activeIndex=2`
    );
  };

  const handleOutline = () => {
    router.push(
      `/pbl/outline?projectId=${encodeURIComponent(project.projectId)}`
    );
  };

  return (
    <Card className="group relative overflow-hidden rounded-2xl border border-slate-200 bg-white shadow-sm ring-1 ring-black/0 transition-all duration-300 hover:shadow-md">
      {/* 封面 */}
      <div className="relative isolate h-40">
        {project.cover ? (
          <>
            <img
              alt={project.projectName}
              className="absolute inset-0 h-full w-full object-cover"
              src={project.cover}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
          </>
        ) : (
          <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 via-sky-50 to-purple-50" />
        )}

        <div className="relative z-10 flex h-full flex-col">
          {/* 顶部图标区域 */}
          <div className="flex flex-1 items-center justify-center">
            {/* <div
              className={clsx(
                'rounded-2xl border p-3 backdrop-blur',
                project.cover
                  ? 'border-white/30 bg-white/20'
                  : 'border-white/60 bg-white/60'
              )}
            >
              <div
                className={clsx(
                  'mx-auto flex h-12 w-12 items-center justify-center rounded-xl shadow-inner',
                  project.cover ? 'bg-white/30' : 'bg-indigo-100'
                )}
              >
                <Activity
                  className={clsx(
                    'h-6 w-6',
                    project.cover ? 'text-white drop-shadow' : 'text-indigo-600'
                  )}
                />
              </div>
            </div> */}
          </div>

          {/* 底部标题区域 */}
          <div className="px-4 pb-4">
            <h3
              className={clsx(
                'line-clamp-2 font-semibold text-xl tracking-tight',
                project.cover ? 'text-white drop-shadow-sm' : 'text-slate-900'
              )}
            >
              {project.projectName}
            </h3>
          </div>
        </div>

        <span className={statusCapsule}>{statusText}</span>
      </div>

      {/* 内容 */}
      <div className="px-2 pt-4">
        <p className="mb-4 line-clamp-2 text-slate-500 text-sm">
          {project.description}
        </p>

        {/* 进度 */}
        <div className="mb-4">
          <div className="mb-1 flex items-center justify-between">
            <span className="text-slate-500 text-sm">项目进度</span>
            <span className="font-medium text-slate-900 text-sm">
              {progress}%
            </span>
          </div>
          <div className="h-2 w-full rounded-full bg-slate-200">
            <div
              className="h-2 rounded-full bg-gradient-to-r from-indigo-500 via-violet-500 to-fuchsia-500 transition-all duration-500"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* 信息 */}
        <div className="mb-4 space-y-2">
          <div className="flex items-center text-slate-600 text-sm">
            <Calendar className="mr-2 h-4 w-4" />
            <span>
              {format(new Date(project.startDate), 'M月d日')} -{' '}
              {format(new Date(project.endDate), 'M月d日')}
            </span>
          </div>
          <div className="flex items-center text-slate-600 text-sm">
            <Users className="mr-2 h-4 w-4" />
            <span>中三班 · 23 名幼儿</span>
          </div>
        </div>

        {/* 标签 */}
        <div className="mb-4 flex flex-wrap gap-2">
          <span className="rounded-md bg-slate-100 px-2 py-1 text-slate-700 text-xs">
            动物认知
          </span>
          <span className="rounded-md bg-slate-100 px-2 py-1 text-slate-700 text-xs">
            科学实验
          </span>
        </div>

        {/* 操作 */}
        <div className="flex items-center justify-between border-slate-100 border-t pt-3">
          <div className="flex gap-2">
            <button
              className="flex items-center rounded-md py-1.5 text-indigo-600 text-sm transition-colors hover:bg-indigo-50"
              onClick={handleView}
              type="button"
            >
              <Eye className="mr-1 h-4 w-4" />
              查看
            </button>
            <button
              className="flex items-center rounded-md px-3 py-1.5 text-slate-600 text-sm transition-colors hover:bg-slate-50"
              onClick={handleEdit}
              type="button"
            >
              <Edit className="mr-1 h-4 w-4" />
              编辑
            </button>
          </div>
          {isActive && (
            <button
              className="flex items-center rounded-full border border-indigo-600 px-4 py-1.5 text-indigo-600 text-sm transition-colors hover:bg-indigo-600 hover:text-white"
              onClick={handleRecord}
              type="button"
            >
              <Send className="mr-1 h-4 w-4" />
              添加记录
            </button>
          )}
          {/* 右上菜单 */}
          <div className=" z-20">
            <PopoverMenu
              actions={[
                {
                  text: '编辑',
                  key: 'outline',
                  icon: <Edit color="#333" />,
                  onClick: handleEdit,
                },
                {
                  text: '删除',
                  key: 'delete',
                  icon: <Trash2 className="text-red-500" />,
                  onClick: () => onDelete?.(project.projectId),
                },
              ]}
              placement="bottom-end"
              trigger="click"
            >
              <button
                aria-label="更多操作"
                className="flex items-center rounded-md p-1.5 text-slate-400 transition-colors hover:bg-white/20 hover:text-white"
                type="button"
              >
                <PiDotsThreeOutlineFill fontSize={16} />
              </button>
            </PopoverMenu>
          </div>
        </div>
      </div>
    </Card>
  );
}
