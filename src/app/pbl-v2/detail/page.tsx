'use client';

import clsx from 'clsx';
import {
  Activity as ActivityIcon,
  ArrowLeft,
  BookOpen,
  Calendar,
  CalendarPlus,
  Camera,
  CheckCircle,
  Clock,
  Edit3,
  Eye,
  FileText,
  FolderOpen,
  Heart,
  Image,
  MessageSquare,
  Pause,
  Play,
  Star,
  Target,
  UserCheck,
  Users,
  Video,
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useMemo } from 'react';
import {
  type Activity,
  type Child,
  mockActivities,
  mockChildren,
  mockProjects,
  type Project,
  type TimelineEvent,
} from '../../../data/mockData';

function ProjectHomePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const projectId = searchParams?.get('projectId') || '';

  // 获取项目数据
  const project = useMemo(() => {
    return mockProjects.find((p: Project) => p.id === projectId);
  }, [projectId]);

  // 获取项目相关活动
  const projectActivities = useMemo(() => {
    return mockActivities
      .filter((activity: Activity) => activity.projectId === projectId)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 3);
  }, [projectId]);

  // 获取参与的儿童
  const participatingChildren = useMemo(() => {
    return mockChildren.filter(
      (child: Child) => child.classId === project?.classId
    );
  }, [project?.classId]);

  if (!project) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-slate-50 via-white to-slate-50">
        <div className="text-center">
          <h2 className="mb-2 font-semibold text-gray-900 text-xl">
            项目未找到
          </h2>
          <p className="mb-4 text-gray-600">请检查项目ID是否正确</p>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: Project['status']) => {
    switch (status) {
      case 'active':
        return 'bg-emerald-100 text-emerald-800 ring-1 ring-inset ring-emerald-200';
      case 'completed':
        return 'bg-blue-100 text-blue-800 ring-1 ring-inset ring-blue-200';
      case 'paused':
        return 'bg-amber-100 text-amber-800 ring-1 ring-inset ring-amber-200';
      case 'draft':
        return 'bg-gray-100 text-gray-800 ring-1 ring-inset ring-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 ring-1 ring-inset ring-gray-200';
    }
  };

  const getStatusText = (status: Project['status']) => {
    switch (status) {
      case 'active':
        return '进行中';
      case 'completed':
        return '已完成';
      case 'paused':
        return '已暂停';
      case 'draft':
        return '草稿';
      default:
        return '未知';
    }
  };

  const getStatusIcon = (status: Project['status']) => {
    switch (status) {
      case 'active':
        return <Play className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'paused':
        return <Pause className="h-4 w-4" />;
      case 'draft':
        return <Edit3 className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
    });
  };

  const getTimelineStatusColor = (status: TimelineEvent['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-emerald-500';
      case 'current':
        return 'bg-blue-500';
      case 'upcoming':
        return 'bg-gray-300';
      default:
        return 'bg-gray-300';
    }
  };

  const getActivityTypeIcon = (type: Activity['type']) => {
    switch (type) {
      case 'observation':
        return <Target className="h-4 w-4" />;
      case 'creation':
        return <Star className="h-4 w-4" />;
      case 'discussion':
        return <MessageSquare className="h-4 w-4" />;
      case 'experiment':
        return <ActivityIcon className="h-4 w-4" />;
      default:
        return <BookOpen className="h-4 w-4" />;
    }
  };

  const getActivityTypeText = (type: Activity['type']) => {
    switch (type) {
      case 'observation':
        return '观察记录';
      case 'creation':
        return '创作活动';
      case 'discussion':
        return '讨论交流';
      case 'experiment':
        return '实验探索';
      default:
        return '其他活动';
    }
  };

  const progressWidth = `${project.progress}%`;

  return (
    <div className="min-h-screen bg-slate-50">
      {/* 顶部英雄区 */}
      <div className="relative overflow-hidden bg-white/70 backdrop-blur supports-[backdrop-filter]:bg-white/60">
        {/* 背景渐变与装饰 */}
        <div className="pointer-events-none absolute inset-0">
          <div className="-left-24 -top-24 absolute h-72 w-72 rounded-full bg-gradient-to-br from-blue-400/20 via-purple-400/10 to-pink-400/10 blur-3xl" />
          {/* <div className="-bottom-24 -right-24 absolute h-64 w-64 rounded-full bg-gradient-to-tr from-cyan-400/20 via-indigo-400/10 to-fuchsia-400/20 blur-3xl" /> */}
        </div>

        <div className="relative px-6 pt-4">
          <div
            className={clsx(
              'absolute top-4 right-4 inline-flex items-center rounded-full px-3 py-1 font-medium text-xs shadow-sm ring-1',
              getStatusColor(project.status)
            )}
          >
            <span className="mr-1 opacity-90">
              {getStatusIcon(project.status)}
            </span>
            <span>{getStatusText(project.status)}</span>
          </div>

          {/* 标题与元信息 */}
          <div className="pb-4">
            <h1 className="mb-4 font-bold text-gray-900 text-xl tracking-tight">
              {project.title}
            </h1>
            <p className="mb-3 text-gray-600 text-sm leading-relaxed">
              {project.description}
            </p>

            {/* 标签 */}
            <div className="mb-4 flex flex-wrap gap-2">
              {project.tags.map((tag: string) => (
                <span
                  className="inline-flex items-center rounded-full bg-white px-2 py-1 text-stone-700 text-xs ring-1 ring-sky-100"
                  key={tag}
                >
                  {tag}
                </span>
              ))}
            </div>

            {/* 基本信息 */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center text-gray-600">
                <Calendar className="mr-2 h-4 w-4" />
                <span>
                  {formatDate(project.startDate)} -{' '}
                  {formatDate(project.endDate)}
                </span>
              </div>
              <div className="flex items-center text-gray-600">
                <Users className="mr-2 h-4 w-4" />
                <span>
                  {project.className} · {project.childrenCount}人
                </span>
              </div>
            </div>
          </div>

          {/* 进度 */}
          <div className="pb-4">
            <div className="mb-2 flex items-center justify-between">
              <span className="font-medium text-gray-700 text-sm">
                项目进度
              </span>
              <span className="text-gray-500 text-sm">{project.progress}%</span>
            </div>
            <div className="relative h-2 w-full rounded-full bg-gray-100">
              <div
                className="h-full rounded-full bg-gradient-to-r from-indigo-300 via-indigo-500 to-purple-600 transition-all duration-500"
                style={{ width: progressWidth }}
              />
              <div className="pointer-events-none absolute inset-0 animate-pulse rounded-full bg-gradient-to-r from-white/0 via-white/20 to-white/0" />
            </div>
          </div>
        </div>
      </div>

      {/* 快捷操作 */}
      <div className="px-4 py-4">
        <div className="rounded-2xl bg-white p-4 shadow-sm ring-1 ring-gray-100">
          <h3 className="mb-4 font-semibold text-base text-gray-900">
            快捷操作
          </h3>
          <div className="grid grid-cols-4 gap-4">
            <button
              className="group hover:-translate-y-0.5 flex flex-col items-center gap-2 transition-all active:translate-y-0 active:opacity-90"
              onClick={() => {
                router.push('/pbl/record/create');
              }}
              type="button"
            >
              <div className="flex h-12 w-12 items-center justify-center rounded-full border border-blue-300 bg-blue-50 shadow-[0_1px_0_rgba(0,0,0,0.02)] transition-all group-hover:border-blue-400 group-hover:bg-blue-100 group-hover:shadow-md">
                <Camera className="h-5 w-5 text-blue-600 transition-transform group-active:scale-95" />
              </div>
              <span className="font-medium text-gray-700 text-xs">
                添加记录
              </span>
            </button>

            <button
              className="group hover:-translate-y-0.5 flex flex-col items-center gap-2 transition-all active:translate-y-0 active:opacity-90"
              onClick={() => {
                // router.push('/pbl-v2/report');
                window.location.href = 'http://192.168.9.24:3000/editor';
              }}
              type="button"
            >
              <div className="flex h-12 w-12 items-center justify-center rounded-full border border-purple-300 bg-purple-50 shadow-[0_1px_0_rgba(0,0,0,0.02)] transition-all group-hover:border-purple-400 group-hover:bg-purple-100 group-hover:shadow-md">
                <FileText className="h-5 w-5 text-purple-600 transition-transform group-active:scale-95" />
              </div>
              <span className="font-medium text-gray-700 text-xs">
                项目报告
              </span>
            </button>
            <button
              className="group hover:-translate-y-0.5 flex flex-col items-center gap-2 transition-all active:translate-y-0 active:opacity-90"
              onClick={() => {
                router.push('/pbl-v2/plan');
              }}
              type="button"
            >
              <div className="flex h-12 w-12 items-center justify-center rounded-full border border-emerald-300 bg-emerald-50 shadow-[0_1px_0_rgba(0,0,0,0.02)] transition-all group-hover:border-emerald-400 group-hover:bg-emerald-100 group-hover:shadow-md">
                <CalendarPlus className="h-5 w-5 text-emerald-600 transition-transform group-active:scale-95" />
              </div>
              <span className="font-medium text-gray-700 text-xs">
                活动计划
              </span>
            </button>
            <button
              className="group hover:-translate-y-0.5 flex flex-col items-center gap-2 transition-all active:translate-y-0 active:opacity-90"
              onClick={() => {
                router.push('/pbl-v2/material');
              }}
              type="button"
            >
              <div className="flex h-12 w-12 items-center justify-center rounded-full border border-orange-300 bg-orange-50 shadow-[0_1px_0_rgba(0,0,0,0.02)] transition-all group-hover:border-orange-400 group-hover:bg-orange-100 group-hover:shadow-md">
                <FolderOpen className="h-5 w-5 text-orange-600 transition-transform group-active:scale-95" />
              </div>
              <span className="font-medium text-gray-700 text-xs">
                相关素材
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* 时间轴 */}
      <div className="px-4 py-4">
        <div className="rounded-2xl bg-white shadow-sm ring-1 ring-gray-100">
          <div className="border-gray-100 border-b p-4">
            <h3 className="font-semibold text-base text-gray-900">
              项目时间轴
            </h3>
          </div>
          <div className="p-4">
            <div className="space-y-4">
              {project.timeline.map((event: TimelineEvent, index: number) => {
                const isLast = index >= project.timeline.length - 1;
                return (
                  <div className="flex items-start gap-3" key={event.id}>
                    <div className="relative flex flex-col items-center">
                      <div
                        className={clsx(
                          'relative h-3 w-3 rounded-full ring-2 ring-white',
                          getTimelineStatusColor(event.status)
                        )}
                      />
                      {!isLast && (
                        <div className="mt-2 h-8 w-0.5 bg-gray-200" />
                      )}
                    </div>
                    <div className="-mt-1.5 min-w-0 flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-gray-900 text-sm">
                          {event.title}
                        </h4>
                        <span className="text-gray-500 text-xs">
                          {formatDate(event.date)}
                        </span>
                      </div>
                      {event.description && (
                        <p className="mt-1 text-gray-600 text-xs">
                          {event.description}
                        </p>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* 活动记录 */}
      <div className="px-4 py-4">
        <div className="rounded-2xl bg-white shadow-sm ring-1 ring-gray-100">
          <div className="border-gray-100 border-b p-4">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-base text-gray-900">
                项目记录
                <span className="pl-4 text-gray-500 text-sm">
                  {projectActivities.length} 条记录
                </span>
              </h3>
              <div>查看全部</div>
            </div>
          </div>

          {projectActivities.length > 0 ? (
            <div className="divide-y divide-gray-100">
              {projectActivities.map((activity: Activity) => (
                <div className="p-4" key={activity.id}>
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0">
                      <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-50 text-blue-600 ring-1 ring-blue-100">
                        {getActivityTypeIcon(activity.type)}
                      </div>
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="mb-1 flex items-center justify-between">
                        <h4 className="font-medium text-gray-900 text-sm">
                          {activity.title}
                        </h4>
                        <span className="text-gray-500 text-xs">
                          {formatDate(activity.date)}
                        </span>
                      </div>
                      <p className="mb-2 text-gray-600 text-sm">
                        {activity.description}
                      </p>

                      {/* 标签与参与 */}
                      <div className="mb-2 flex items-center gap-2">
                        <span className="inline-flex items-center rounded-full bg-gray-50 px-2 py-1 text-gray-700 text-xs ring-1 ring-gray-200">
                          {getActivityTypeText(activity.type)}
                        </span>
                        {activity.participants.length > 0 && (
                          <span className="text-gray-500 text-xs">
                            {activity.participants.length} 名幼儿参与
                          </span>
                        )}
                      </div>

                      {/* 媒体信息 */}
                      {(activity.photos.length > 0 ||
                        activity.videos.length > 0) && (
                        <div className="flex items-center gap-4 text-gray-500 text-xs">
                          {activity.photos.length > 0 && (
                            <div className="flex items-center">
                              <Image className="mr-1 h-3 w-3" />
                              {activity.photos.length} 张照片
                            </div>
                          )}
                          {activity.videos.length > 0 && (
                            <div className="flex items-center">
                              <Video className="mr-1 h-3 w-3" />
                              {activity.videos.length} 个视频
                            </div>
                          )}
                        </div>
                      )}

                      {/* 照片预览 */}
                      {activity.photos.length > 0 && (
                        <div className="mt-2 flex gap-2">
                          {activity.photos.slice(0, 3).map((photo, index) => (
                            <img
                              alt={`活动照片 ${index + 1}`}
                              className="h-12 w-12 rounded-lg object-cover ring-1 ring-gray-200"
                              key={photo}
                              src={photo}
                            />
                          ))}
                          {activity.photos.length > 3 && (
                            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-gray-50 text-gray-500 text-xs ring-1 ring-gray-200">
                              +{activity.photos.length - 3}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-8 text-center">
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-50 text-gray-400 ring-1 ring-gray-100">
                <ActivityIcon className="h-8 w-8" />
              </div>
              <h4 className="mb-2 font-medium text-gray-900 text-sm">
                暂无活动记录
              </h4>
              <p className="mb-4 text-gray-500 text-sm">开始记录第一个活动吧</p>
              <button
                className="hover:-translate-y-0.5 inline-flex items-center rounded-xl bg-blue-600 px-4 py-2 font-medium text-sm text-white shadow-sm transition-all hover:bg-blue-700 hover:shadow-md active:translate-y-0"
                type="button"
              >
                <Camera className="mr-2 h-4 w-4" />
                记录活动
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 成员管理 */}
      <div className="hidden px-4 py-4">
        <div className="rounded-2xl bg-white shadow-sm ring-1 ring-gray-100">
          <div className="border-gray-100 border-b p-4">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-base text-gray-900">
                项目成员
              </h3>
              <span className="text-gray-500 text-sm">
                {project.groups.length} 个小组
              </span>
            </div>
          </div>

          <div className=" p-4">
            <div className="space-y-4">
              {project.groups.map((group) => (
                <div
                  className="rounded-xl border border-gray-200 p-3 transition-colors hover:bg-gray-50"
                  key={group.id}
                >
                  <div className="mb-3 flex items-center justify-between">
                    <div className="flex items-center">
                      <span
                        className="mr-2 inline-block h-3 w-3 rounded-full ring-2 ring-white"
                        style={{ backgroundColor: group.color }}
                      />
                      <span className="font-medium text-gray-900 text-sm">
                        {group.name}
                      </span>
                    </div>
                    <span className="text-gray-500 text-xs">
                      {group.children.length} 名成员
                    </span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {group.children.map((childId) => {
                      const child = participatingChildren.find(
                        (c) => c.id === childId
                      );
                      if (!child) {
                        return null;
                      }
                      return (
                        <div
                          className="hover:-translate-y-0.5 flex items-center gap-2 rounded-lg bg-gray-50 px-2 py-1 ring-1 ring-gray-200 transition-all"
                          key={child.id}
                        >
                          <img
                            alt={child.name}
                            className="h-6 w-6 rounded-full object-cover ring-1 ring-gray-200"
                            src={child.avatar}
                          />
                          <span className="text-gray-700 text-xs">
                            {child.name}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 数据概览 */}
      <div className="px-4 pt-4 pb-8">
        <div className="rounded-2xl bg-white shadow-sm ring-1 ring-gray-100">
          <div className="border-gray-100 border-b p-4">
            <h3 className="font-semibold text-base text-gray-900">数据概览</h3>
          </div>

          <div className="p-4">
            <div className="grid grid-cols-2 gap-4">
              {/* <div className="rounded-xl bg-gradient-to-br from-blue-50 to-indigo-50 p-3 ring-1 ring-blue-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-blue-700 text-xs">
                      活动完成数
                    </p>
                    <p className="font-bold text-blue-900 text-lg">
                      {project.completedActivities}/{project.activitiesCount}
                    </p>
                  </div>
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100">
                    <CheckCircle className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
              </div> */}

              <div className="rounded-xl bg-gradient-to-br from-emerald-50 to-green-50 p-3 ring-1 ring-emerald-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-emerald-700 text-xs">
                      幼儿参与度
                    </p>
                    <p className="font-bold text-emerald-900 text-lg">
                      {Math.round(
                        (project.completedActivities /
                          project.activitiesCount) *
                          100
                      )}
                      %
                    </p>
                  </div>
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-emerald-100">
                    <UserCheck className="h-4 w-4 text-emerald-600" />
                  </div>
                </div>
              </div>

              <div className="rounded-xl bg-gradient-to-br from-purple-50 to-fuchsia-50 p-3 ring-1 ring-purple-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-purple-700 text-xs">
                      观察记录数
                    </p>
                    <p className="font-bold text-lg text-purple-900">
                      {project.observationCount}
                    </p>
                  </div>
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-purple-100">
                    <Eye className="h-4 w-4 text-purple-600" />
                  </div>
                </div>
              </div>

              {/* <div className="rounded-xl bg-gradient-to-br from-orange-50 to-amber-50 p-3 ring-1 ring-orange-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-orange-700 text-xs">
                      家长参与率
                    </p>
                    <p className="font-bold text-lg text-orange-900">
                      {project.parentParticipationRate}%
                    </p>
                  </div>
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-orange-100">
                    <Heart className="h-4 w-4 text-orange-600" />
                  </div>
                </div>
              </div> */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ProjectHomePage;
