/** biome-ignore-all lint/suspicious/noExplicitAny: <explanation> */
'use client';

import {
  Button,
  Dialog,
  DotLoading,
  Modal,
  SpinLoading,
  Toast,
} from 'antd-mobile';
import { useSetAtom } from 'jotai';
import {
  Calendar,
  CalendarRange,
  ChevronDown,
  ChevronUp,
  ClipboardList,
  Edit3,
  Image as ImageIcon,
  MapPin,
  MessageSquare,
  Plus,
  RefreshCw,
  Share2,
  Trash2,
  Users,
  Video,
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { memo, useEffect, useRef, useState } from 'react';
import { useImmer } from 'use-immer';
import {
  aiGenerateContent,
  deleteObservation,
  getObservationDetail,
  updateObservation,
} from '@/api/pbl';
import StudentPicker, {
  type StudentPickerRef,
} from '@/components/StudentPicker';
import { mediaAtom, studentAtom } from '@/store/pbl';
import { share } from '@/utils/index';
import Media from '../detail/components/Media';
import EditTextDialog from '../detail/day/components/EditTextDialog';
import StudentCard from './components/StudentCard';

interface AbilityTarget {
  id: number;
  name: string;
  completed: boolean;
}

interface AbilityCategory {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  targets: AbilityTarget[];
  subCategories?: {
    name: string;
    targets: AbilityTarget[];
  }[];
}

interface Student {
  id: number; // 与StudentCard组件中的类型保持一致
  name: string;
  avatar: string;
  abilities: string[];
  abilityCategories?: AbilityCategory[];
  progress?: string;
  evaluation: {
    observationId: string;
    observationText: string; // 与StudentCard组件中的类型保持一致
    abilities: {
      abilityId: string;
    }[];
  };
}

interface MediaItem {
  mediaId?: number;
  url: string;
  name?: string;
  duration?: string | number;
  type: number | string;
  cover?: string;
}

// 定义与mediaAtom中medias数组类型匹配的接口

interface Record {
  date: string;
  source: string;
  content: string;
  nextStepPlan: string;
  students: Student[];
  medias: MediaItem[];
  createUser: {
    name: string;
    avatar: string;
  };
  createTime: string;
  deptId: string;
  conversation: ConversationItem[];
  tags: {
    tagId: number;
    tagName: string;
  }[];
  zone: {
    zoneName: string;
  };
}

// 为StudentCard组件定义兼容的Student类型

interface AssociatedStudentsProps {
  students: Student[];
  deptId: string;
  observationId: string;
}

const AssociatedStudents = memo(
  ({ students, deptId, observationId }: AssociatedStudentsProps) => {
    // console.log('🚀 ~ students:', students);
    const router = useRouter();
    const [expandedStudents, setExpandedStudents] = useState<number[]>([]);
    const studentPickerRef = useRef<StudentPickerRef>(null);
    const setStudent = useSetAtom(studentAtom);

    const handleSelectStudent = async () => {
      if (studentPickerRef.current) {
        try {
          const selectedStudent =
            await studentPickerRef.current.selectStudentsByClassId();
          if (selectedStudent) {
            console.log('选中的学生：', selectedStudent);
            if (
              students.some(
                (student) => student.id === Number(selectedStudent.studentId)
              )
            ) {
              Toast.show({
                content: '学生已存在',
                position: 'bottom',
              });
              return;
            }
            setStudent({
              id: selectedStudent.studentId,
              name: selectedStudent.studentName,
              avatar: selectedStudent.avatar,
              deptId,
              evaluation: {
                evaluationId: '',
                observationId,
                abilities: [],
              },
            });
            router.push('/pbl/record/update/evaluation');
          }
        } catch (error) {
          console.error('选择学生失败：', error);
        }
      }
    };

    // 切换学生卡片展开/折叠状态
    const toggleStudentExpand = (studentId: number, e: React.MouseEvent) => {
      e.stopPropagation(); // 阻止事件冒泡，避免触发卡片点击事件
      setExpandedStudents((prev) =>
        prev.includes(studentId)
          ? prev.filter((id) => id !== studentId)
          : [...prev, studentId]
      );
    };

    // 检查学生是否展开
    const isStudentExpanded = (studentId: number) =>
      expandedStudents.includes(studentId);

    return (
      <div className="mb-6 px-4">
        <h3 className="mb-2 flex items-center justify-between gap-2 font-bold text-md">
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5 text-indigo-500" />
            <span>关联学生</span>
          </div>
          <div>
            <button
              className="flex items-center gap-1 rounded-full bg-indigo-50 px-3 py-1.5 text-indigo-600 text-xs transition-colors hover:bg-indigo-100"
              onClick={() => handleSelectStudent()}
              type="button"
            >
              <Plus className="h-4 w-4" /> 新增学生
            </button>
          </div>
        </h3>
        <div className="space-y-3">
          {students.map((student) => (
            <StudentCard
              isStudentExpanded={
                students.length === 1 ? () => true : isStudentExpanded
              }
              key={student.id}
              student={student}
              toggleStudentExpand={toggleStudentExpand}
            />
          ))}
        </div>
        <StudentPicker classId={deptId} hideUI={true} ref={studentPickerRef} />
      </div>
    );
  }
);

// 重新生成Modal组件
interface RegenerateModalProps {
  visible: boolean;
  observationId: string;
  currentContent: string;
  nextStepPlan: string;
  onClose: () => void;
  onSuccess: (newContent: string) => void;
}

const RegenerateModal = ({
  visible,
  observationId,
  currentContent,
  nextStepPlan,
  onClose,
  onSuccess,
}: RegenerateModalProps) => {
  const [prompt, setPrompt] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [editableContent, setEditableContent] = useState('');
  const [showConfirm, setShowConfirm] = useState(false);

  const generateContent = async () => {
    if (!prompt.trim()) {
      Toast.show({
        content: '请输入补充信息',
        position: 'bottom',
      });
      return;
    }

    setIsLoading(true);
    try {
      const query = `
        基于以下观察记录内容，根据用户的补充要求重新生成观察记录：
        
        原始内容：${currentContent}
        
        用户补充要求：${prompt}
        
        请生成一份新的观察记录内容，要求详细、专业，符合学前教育观察记录的标准。直接输出记录的正文内容，不要带格式，也不需要观察记录的其他额外信息。
      `;

      const response: any = await aiGenerateContent({
        conversation_id: '',
        files: [],
        inputs: [],
        query,
        response_mode: 'blocking',
      });

      if (response?.answer) {
        setEditableContent(response.answer);
        setShowConfirm(true);
      } else {
        Toast.show({
          content: '生成失败，请重试',
          position: 'bottom',
        });
      }
    } catch (error) {
      console.error('生成内容失败：', error);
      Toast.show({
        content: '生成失败，请重试',
        position: 'bottom',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleConfirmUse = async () => {
    try {
      // 使用现有的onRecordUpdate逻辑
      await updateObservation(observationId, {
        title: '',
        content: editableContent,
        nextStepPlan, // 保持原有的nextStepPlan
        observationId,
      });

      Toast.show({
        content: '更新成功',
        position: 'bottom',
      });

      onSuccess(editableContent);
      handleClose();
    } catch (error) {
      console.error('更新失败：', error);
      Toast.show({
        content: '更新失败，请重试',
        position: 'bottom',
      });
    }
  };

  const handleClose = () => {
    setPrompt('');
    setEditableContent('');
    setShowConfirm(false);
    setIsLoading(false);
    onClose();
  };

  if (!visible) {
    return null;
  }

  return (
    <>
      {/* 输入补充信息的Modal */}
      {!showConfirm && (
        <Modal
          closeOnMaskClick={!isLoading}
          content={
            <div className="p-2">
              <div className="mb-4">
                <h3 className="text-center font-bold text-lg">
                  重新生成观察记录
                </h3>
                <p className="mt-2 text-center text-gray-500 text-sm">
                  补充更多信息重新生成新的观察记录
                </p>
              </div>

              <textarea
                className="mb-4 w-full rounded border border-gray-300 p-3"
                disabled={isLoading}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="请输入你的补充信息，信息越详细，AI生成的内容越准确"
                rows={5}
                value={prompt}
              />

              {isLoading ? (
                <div className="flex flex-col items-center justify-center py-4">
                  <div className="text-blue-500">
                    <DotLoading />
                  </div>
                  <p className="mt-2 text-gray-500">正在生成内容，请稍候...</p>
                </div>
              ) : (
                <div className="flex justify-end gap-2">
                  <Button color="default" onClick={handleClose}>
                    取消
                  </Button>
                  <Button color="primary" onClick={generateContent}>
                    重新生成
                  </Button>
                </div>
              )}
            </div>
          }
          onClose={handleClose}
          showCloseButton={!isLoading}
          visible={true}
        />
      )}

      {/* 确认使用生成内容的Modal */}
      {showConfirm && (
        <Modal
          closeOnMaskClick={false}
          content={
            <div className="p-2">
              <div className="mb-4">
                <h3 className="text-center font-bold text-lg">
                  确认使用生成的内容
                </h3>
              </div>

              <div className="mb-4 max-h-60 overflow-y-auto rounded border bg-gray-50 p-3">
                <textarea
                  className="h-40 w-full resize-none border-none bg-transparent text-sm leading-relaxed outline-none"
                  onChange={(e) => setEditableContent(e.target.value)}
                  placeholder="编辑生成的内容..."
                  value={editableContent}
                />
              </div>

              <div className="flex justify-end gap-2">
                <Button color="default" onClick={handleClose}>
                  放弃
                </Button>
                <Button color="primary" onClick={handleConfirmUse}>
                  确定使用
                </Button>
              </div>
            </div>
          }
          showCloseButton={false}
          visible={true}
        />
      )}
    </>
  );
};

interface FooterActionsProps {
  source: string;
  observationId: string;
  currentContent: string;
  nextStepPlan: string;
  onContentUpdate: (newContent: string) => void;
}

function FooterActions({
  observationId,
  currentContent,
  source,
  nextStepPlan,
  onContentUpdate,
}: FooterActionsProps) {
  const router = useRouter();
  const [showRegenerateModal, setShowRegenerateModal] = useState(false);

  const handleRegenerate = () => {
    setShowRegenerateModal(true);
  };

  const handleDelete = () => {
    Dialog.confirm({
      content: '删除后记录将无法恢复',
      cancelText: '取消',
      confirmText: <div className="text-red-400">确认删除</div>,
      onConfirm: async () => {
        await deleteObservation(observationId);
        Toast.show({
          icon: 'success',
          content: '删除成功',
          position: 'bottom',
        });
        router.back();
      },
    });
  };

  return (
    <div className="sticky bottom-0">
      <div className="flex items-center justify-around rounded-2xl bg-card p-2 shadow-md">
        <button
          className="flex flex-col items-center rounded-lg text-muted-foreground transition-colors hover:text-primary"
          onClick={() => {
            const shareData = {
              type: 0,
              title: '老师分享了新的观察记录',
              description: '',
              thumbImage: '',
              url: `${window.location.href}`,
            };
            share(shareData);
          }}
          type="button"
        >
          <Share2 className="mb-1 h-5 w-5" />
          <span className="text-xs">分享</span>
        </button>
        {source === '1' && (
          <button
            className="flex flex-col items-center rounded-lg text-primary transition-colors hover:text-primary-700"
            onClick={handleRegenerate}
            type="button"
          >
            <RefreshCw className="mb-1 h-5 w-5" />
            <span className="text-xs">重新生成</span>
          </button>
        )}
        <button
          className="flex flex-col items-center rounded-lg text-red-500 transition-colors hover:text-red-700"
          onClick={handleDelete}
          type="button"
        >
          <Trash2 className="mb-1 h-5 w-5" />
          <span className="text-xs">删除</span>
        </button>
      </div>

      {/* RegenerateModal */}
      <RegenerateModal
        currentContent={currentContent}
        nextStepPlan={nextStepPlan}
        observationId={observationId}
        onClose={() => setShowRegenerateModal(false)}
        onSuccess={onContentUpdate}
        visible={showRegenerateModal}
      />
    </div>
  );
}

interface ConversationItem {
  speaker: string;
  text: string;
}

/** 图片网格：每行3列，最多显示9张，超过则在第9张叠加蒙版显示“{剩余}+” */
const ImageGrid = () => {
  // 使用 images.unsplash.com 的稳定资源 ID，确保可访问
  const photoIds = [
    '1500530855697-b586d89ba3ee',
    '1503023345310-bd7c1de61c7d',
    '1502082553048-f009c37129b9',
    '1500534314209-a25ddb2bd429',
    '1503602642458-232111445657',
    '1501785888041-af3ef285b470',
    '1506277886164-e25aa3f4ef7f',
    '1472214103451-9374bd1c798e',
    '1491553895911-0055eca6402d',
    '1519681393784-d120267933ba',
    '1520975940462-20b88b061389',
    '1520975922131-c4ec4258b9a7',
    '1469474968028-56623f02e42e',
    '1470770903676-69b98201ea1c',
    '1441974231531-c6227db76b6e',
    '1436891620584-47fd0e565afb',
    '1460353581641-37baddab0fa2',
    '1438109491414-7198515b166b',
    '1429087969512-1e85aab2683d',
    '1438761681033-6461ffad8d80',
    '1462332420958-a05d1e002413',
    '1470071459604-3b5ec3a7fe05',
    '1507149833265-60c372daea22',
    '1526498460520-4c246339dccb',
    '1500534314209-a25ddb2bd429',
  ];
  const mockImages: { id: number; url: string; alt: string }[] = photoIds.map(
    (pid, i) => ({
      id: i + 1,
      url: `https://images.unsplash.com/photo-${pid}?auto=format&fit=crop&w=600&q=80`,
      alt: `相关图片 ${i + 1}`,
    })
  );

  const maxShow = 9;
  const showList = mockImages.slice(0, maxShow);
  const leftover =
    mockImages.length > maxShow ? mockImages.length - maxShow : 0;

  return (
    <div className="grid grid-cols-3 gap-2">
      {showList.map((img, idx) => {
        const isLastCover = idx === maxShow - 1 && leftover > 0;
        return (
          <div className="relative overflow-hidden rounded-lg" key={img.id}>
            <img
              alt={img.alt}
              className="h-24 w-full object-cover"
              src={img.url}
            />
            {isLastCover && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/50">
                <span className="font-semibold text-lg text-white">
                  {leftover}+
                </span>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default function App() {
  const searchParams = useSearchParams();
  const observationId = searchParams?.get('observationId');
  const [isExpanded, setIsExpanded] = useState(false);

  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [record, setRecord] = useImmer<Record>({
    date: '',
    content: '',
    nextStepPlan: '',
    students: [] as Student[],
    medias: [] as MediaItem[],
    createUser: {
      name: '',
      avatar: '',
    },
    createTime: '',
    deptId: '',
    conversation: [],
    zone: {
      zoneName: '',
    },
    source: '1',
    tags: [],
  });

  const [isEditRecordDialogVisible, setIsEditRecordDialogVisible] =
    useState(false);
  const [isEditSuggestDialogVisible, setIsEditSuggestDialogVisible] =
    useState(false);
  const setMedia = useSetAtom(mediaAtom);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '观察记录详情';
    }
  }, []);

  useEffect(() => {
    if (observationId) {
      setLoading(true);
      getObservationDetail(observationId)
        .then((res) => {
          setRecord(res.data || res);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [observationId]);

  const onRecordUpdate = async (text: string | undefined) => {
    if (!text) {
      return;
    }
    if (!observationId) {
      return;
    }
    await updateObservation(observationId, {
      title: '',
      content: text,
      nextStepPlan: record?.nextStepPlan,
      observationId,
    });
    Toast.show({
      content: '更新成功',
    });
  };

  const onSuggestUpdate = async (text: string | undefined) => {
    if (!text) {
      return;
    }
    if (!observationId) {
      return;
    }

    await updateObservation(observationId, {
      title: '',
      nextStepPlan: text,
      content: record?.content,
      observationId,
    });
    Toast.show({
      content: '更新成功',
    });
  };

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <SpinLoading color="primary" />
      </div>
    );
  }

  return (
    <div className="relative flex h-screen flex-col">
      {/* 可滚动内容区域 */}
      <div className="flex-grow overflow-y-auto pb-20">
        <div className="my-6 px-4">
          <div className="mb-6 flex items-center gap-2">
            <div className="flex items-center">
              <picture>
                <img
                  alt="老师头像"
                  className="h-10 w-10 rounded-full"
                  src={record.createUser?.avatar || ''}
                />
              </picture>
            </div>
            <div>
              <span className="text-base">{record.createUser?.name}</span>
              <div className="flex items-center gap-1 text-gray-500 text-xs">
                <Calendar className="h-3 w-3" />
                <span>{record.createTime}</span>
              </div>
            </div>
          </div>
          {/* 观察地点 */}
          {record.zone?.zoneName && (
            <div className="mb-6 flex items-center">
              <h3 className="flex items-center gap-2 font-bold text-base text-gray-700">
                <MapPin className="h-5 w-5 text-green-500" />
                <span>观察地点：</span>
              </h3>
              <div className="text-base text-gray-700">
                {record.zone.zoneName}
              </div>
            </div>
          )}
          {/* 观察记录内容 */}
          <div className="mb-6">
            <h3 className="mb-2 flex items-center gap-2 font-bold text-base text-gray-700">
              <ClipboardList className="h-5 w-5 text-amber-500" />
              <span>记录内容</span>
            </h3>
            <div className="rounded-2xl bg-amber-50 p-4 text-base text-gray-700 leading-relaxed">
              {record.content}
              <button
                className="inline-block p-1 text-gray-500 transition-colors hover:text-primary"
                onClick={() => setIsEditRecordDialogVisible(true)}
                type="button"
              >
                <Edit3 className="h-4 w-4 text-indigo-500" />
              </button>
            </div>
          </div>

          {/* 标签 */}
          {record.tags && Array.isArray(record.tags) && (
            <div className="mb-6 flex flex-wrap gap-2">
              {record.tags?.map((tag: any, index) => {
                const tagColors = [
                  'bg-green-50 text-green-700',
                  'bg-orange-50 text-orange-700',
                  'bg-purple-50 text-purple-700',
                  'bg-blue-50 text-blue-700',
                  'bg-pink-50 text-pink-700',
                ];
                return (
                  <span
                    className={`mr-2 rounded-full px-3 py-1.5 text-sm ${
                      tagColors[index % tagColors.length]
                    }`}
                    key={tag.tagId}
                  >
                    #{tag.tagName}
                  </span>
                );
              })}
            </div>
          )}
        </div>

        <AssociatedStudents
          deptId={record.deptId}
          observationId={observationId || ''}
          students={record.students || []}
        />
        <div className="px-4">
          <h3 className="mb-3 flex items-center justify-between font-bold text-gray-700 text-md">
            <div className="flex items-center gap-2">
              <Video className="h-5 w-5 text-green-500" />
              <span>关联视频</span>
              <span className="font-normal text-gray-500 text-xs">
                ({record.medias.length})
              </span>
            </div>
            <div>
              <button
                className="flex items-center gap-1 rounded-full bg-gray-50 px-3 py-1.5 text-gray-600 text-xs transition-colors hover:bg-gray-100"
                onClick={() => {
                  setMedia({
                    observationId: observationId || '',
                    deptId: record.deptId,
                    medias: record.medias.map((media: any) => {
                      let mediaType: string;
                      if (media.type === 1) {
                        mediaType = 'image';
                      } else if (media.type === 2) {
                        mediaType = 'video';
                      } else {
                        mediaType = 'audio';
                      }

                      return {
                        ...media,
                        status: 'completed',
                        id: media.mediaId,
                        type: mediaType,
                      };
                    }),
                  });
                  router.push('/pbl/record/update/media');
                }}
                type="button"
              >
                {record.medias.length > 0 ? '修改文件' : '添加文件'}
              </button>
            </div>
          </h3>
          <Media media={record.medias} />
        </div>

        {/* 相关图片（模拟数据） */}
        <div className="mt-6 px-4">
          <h3 className="mb-3 flex items-center gap-2 font-bold text-gray-700 text-md">
            <ImageIcon className="h-5 w-5 text-pink-500" />
            <span>相关图片</span>
          </h3>
          <ImageGrid />
        </div>
        {/* 下一步计划 */}
        {!!record.nextStepPlan && (
          <div className="mb-6 px-4">
            <h2 className="mb-2 flex items-center gap-2 font-semibold text-base">
              <CalendarRange className="h-4 w-4 text-teal-400" />
              下一步计划
            </h2>

            <div className="rounded-lg border border-teal-200 bg-teal-50 p-3">
              <div className="whitespace-pre-line text-gray-700 text-sm">
                {record.nextStepPlan}
                <button
                  className="inline-block p-1 text-gray-500 transition-colors hover:text-primary"
                  onClick={() => setIsEditSuggestDialogVisible(true)}
                  type="button"
                >
                  <Edit3 className="h-4 w-4 text-blue-500" />
                </button>
              </div>
            </div>
          </div>
        )}
        {!!record.conversation.length && (
          <div className="mb-6 px-4">
            <h3 className="mb-3 flex items-center gap-2 font-bold text-gray-700 text-md">
              <MessageSquare className="h-5 w-5 text-blue-500" />
              <span>人物对话</span>
              <span className="font-normal text-gray-500 text-xs">
                ({record.conversation.length})
              </span>
            </h3>
            <div className="space-y-4">
              {(isExpanded
                ? record.conversation
                : record.conversation.slice(-5)
              ).map((item: ConversationItem, index: number) => {
                const isTeacher = item.speaker === '老师';
                return (
                  <div
                    className={`flex ${isTeacher ? 'justify-start' : 'justify-end'}`}
                    key={`${item.speaker}-${index}`}
                  >
                    <div className="max-w-[85%]">
                      <div
                        className={`mb-1 flex items-center gap-2 ${isTeacher ? '' : 'justify-end'}`}
                      >
                        <div
                          className={`rounded py-0.5 font-bold text-sm ${isTeacher ? ' text-blue-700' : ' text-green-700'}`}
                        >
                          {item.speaker}
                        </div>
                      </div>
                      <div
                        className={`break-words rounded-2xl p-3 ${
                          isTeacher
                            ? 'rounded-tl-none border border-blue-100 bg-blue-50'
                            : 'rounded-tr-none border border-green-100 bg-green-50'
                        }`}
                      >
                        <p className="whitespace-pre-line text-sm leading-relaxed">
                          {item.text}
                        </p>
                      </div>
                    </div>
                  </div>
                );
              })}
              {record.conversation.length > 5 &&
                (isExpanded ? (
                  <button
                    className="flex w-full items-center justify-center gap-1 rounded-lg bg-gray-50 px-4 py-2 text-gray-500 text-sm transition-colors hover:text-gray-700"
                    onClick={() => setIsExpanded(false)}
                    type="button"
                  >
                    <ChevronUp className="h-4 w-4" />
                    收起对话
                  </button>
                ) : (
                  <button
                    className="flex w-full items-center justify-center gap-1 rounded-lg bg-gray-50 px-4 py-2 text-gray-500 text-sm transition-colors hover:text-gray-700"
                    onClick={() => setIsExpanded(true)}
                    type="button"
                  >
                    <ChevronDown className="h-4 w-4" />
                    显示全部 {record.conversation.length} 条对话
                  </button>
                ))}
            </div>
          </div>
        )}
      </div>
      <FooterActions
        currentContent={record.content}
        nextStepPlan={record.nextStepPlan}
        observationId={observationId || ''}
        onContentUpdate={(newContent) => {
          setRecord((draft) => {
            draft.content = newContent;
          });
        }}
        source={record.source}
      />
      {/* 编辑记录内容对话框 */}
      <EditTextDialog
        emptyMessage="记录内容不能为空"
        initialContent={record.content}
        onClose={() => setIsEditRecordDialogVisible(false)}
        onSave={async (content) => {
          await onRecordUpdate(content);
        }}
        onSuccess={(newContent) => {
          setRecord((draft) => {
            draft.content = newContent;
          });
        }}
        placeholder="请输入记录内容"
        title="编辑记录内容"
        visible={isEditRecordDialogVisible}
      />

      {/* 编辑下一步计划对话框 */}
      <EditTextDialog
        emptyMessage="下一步计划不能为空"
        initialContent={record.nextStepPlan}
        onClose={() => setIsEditSuggestDialogVisible(false)}
        onSave={async (content) => {
          await onSuggestUpdate(content);
        }}
        onSuccess={(newContent) => {
          setRecord((draft) => {
            draft.nextStepPlan = newContent;
          });
        }}
        placeholder="请输入下一步计划"
        title="编辑下一步计划"
        visible={isEditSuggestDialogVisible}
      />
    </div>
  );
}
