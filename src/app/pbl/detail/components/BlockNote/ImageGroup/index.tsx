import { createReactBlockSpec } from '@blocknote/react';
import type { ImageUploadItem } from 'antd-mobile/es/components/image-uploader';
import { useAtom } from 'jotai';
import { useEffect } from 'react';
import { isEditableAtom } from '@/store/pbl';
import ImageUploader from './ImageUploader';

type FileItem = ImageUploadItem & { type: 'image' | 'video' };

export const ImageGrid = createReactBlockSpec(
  {
    type: 'imageGrid',
    propSchema: {
      images: {
        default: '',
      },
    },
    content: 'none',
    isSelectable: false,
  },
  {
    render: ({ block, editor }) => {
      const [isEditable] = useAtom(isEditableAtom);

      // 确保block.props.images存在且是数组
      useEffect(() => {
        try {
          const images = block.props.images
            ? JSON.parse(block.props.images)
            : [];
          if (!Array.isArray(images)) {
            editor.updateBlock(block, {
              props: {
                ...block.props,
                images: JSON.stringify([]),
              },
            });
          }
        } catch (e) {
          editor.updateBlock(block, {
            props: {
              ...block.props,
              images: JSON.stringify([]),
            },
          });
        }
      }, [block, editor]);

      let currentImages: FileItem[] = [];
      try {
        currentImages = block.props.images
          ? JSON.parse(block.props.images)
          : [];
      } catch (e) {
        currentImages = [];
      }

      return (
        <div className="">
          <ImageUploader
            onChange={(newImages) => {
              console.log('🚀 ~ newImages:', newImages);
              // 确保在更新之前数据是有效的
              if (Array.isArray(newImages)) {
                editor.updateBlock(block, {
                  props: {
                    ...block.props,
                    images: JSON.stringify(newImages),
                  },
                });
              }
            }}
            readOnly={!isEditable}
            value={currentImages}
          />
        </div>
      );
    },
  }
);
