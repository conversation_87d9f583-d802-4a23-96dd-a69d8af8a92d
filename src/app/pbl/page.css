:root {
  --primary: #5e81f4;
  --primary-light: #e5e9fa;
  --secondary: #ff7ac6;
  --success: #7ce7ac;
  --warning: #f4be5e;
  --danger: #ff808b;
  --dark: #1c1d21;
  --light: #f5f5fb;
  --gray: #8181a5;
}
.card {
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.progress-bar {
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  transition: width 1s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.delay-100 {
  animation-delay: 0.1s;
}
.delay-200 {
  animation-delay: 0.2s;
}
.delay-300 {
  animation-delay: 0.3s;
}

.fluid-gradient {
  background: linear-gradient(
    135deg,
    rgba(94, 129, 244, 0.9),
    rgba(255, 122, 198, 0.9)
  );
  position: relative;
  overflow: hidden;
}

.fluid-gradient::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle,
    rgba(94, 129, 244, 0.1) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  animation: rotate 15s linear infinite;
  z-index: 0;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
