// Mock数据文件
export interface Project {
  id: string;
  title: string;
  description: string;
  status: 'draft' | 'active' | 'completed' | 'paused';
  progress: number; // 0-100
  startDate: string;
  endDate: string;
  classId: string;
  className: string;
  childrenCount: number;
  activitiesCount: number;
  completedActivities: number;
  lastActivity: string;
  coverImage?: string;
  tags: string[];
  timeline: TimelineEvent[];
  groups: ProjectGroup[];
  observationCount: number;
  parentParticipationRate: number;
}

export interface TimelineEvent {
  id: string;
  title: string;
  date: string;
  status: 'completed' | 'current' | 'upcoming';
  description?: string;
}

export interface ProjectGroup {
  id: string;
  name: string;
  children: string[];
  color: string;
}

export interface DashboardStats {
  activeProjects: number;
  weeklyActivities: number;
  pendingEvaluations: number;
  parentParticipation: number;
}

export interface Activity {
  id: string;
  projectId: string;
  title: string;
  description: string;
  date: string;
  type: 'observation' | 'creation' | 'discussion' | 'experiment';
  photos: string[];
  videos: string[];
  notes: string;
  participants: string[];
}

export interface Child {
  id: string;
  name: string;
  avatar: string;
  classId: string;
  age: number;
}

// Mock项目数据
export const mockProjects: Project[] = [
  {
    id: '1',
    title: '春天的小种子',
    description:
      '通过观察种子发芽过程，让孩子们了解植物的生长规律，培养观察能力和科学探究精神。',
    status: 'active',
    progress: 65,
    startDate: '2024-03-01',
    endDate: '2024-04-15',
    classId: 'class-1',
    className: '大班一班',
    childrenCount: 28,
    activitiesCount: 12,
    completedActivities: 8,
    lastActivity: '观察豆芽生长第7天',
    coverImage: '/images/seed-project.jpg',
    tags: ['科学探究', '自然观察', '生命教育'],
    timeline: [
      {
        id: 't1',
        title: '项目启动会：激发种子探索兴趣',
        date: '2024-03-01',
        status: 'completed',
        description: '通过故事和实物展示介绍种子主题，激发幼儿好奇心与参与热情',
      },
      {
        id: 't2',
        title: '种子观察活动：形状与特征探索',
        date: '2024-03-05',
        status: 'completed',
        description:
          '引导幼儿使用放大镜观察不同种子的形状、大小、颜色和纹理特征',
      },
      {
        id: 't3',
        title: '种植实验：亲手种下豆芽',
        date: '2024-03-10',
        status: 'completed',
        description: '幼儿亲手将豆子放置于湿润纸巾中，学习种植步骤并开始记录',
      },
      {
        id: 't4',
        title: '每日生长观察与记录',
        date: '2024-03-20',
        status: 'current',
        description: '每日测量豆芽高度，观察根茎叶变化，培养持续观察与记录习惯',
      },
      {
        id: 't5',
        title: '成果展示：我的豆芽成长日记',
        date: '2024-04-10',
        status: 'upcoming',
        description: '整理观察记录，制作成长日记并分享发现，提升表达与总结能力',
      },
    ],
    groups: [
      {
        id: 'g1',
        name: '小种子组',
        children: ['child-1', 'child-2', 'child-3', 'child-4'],
        color: '#10B981',
      },
      {
        id: 'g2',
        name: '小芽芽组',
        children: ['child-5', 'child-6', 'child-7', 'child-8'],
        color: '#3B82F6',
      },
      {
        id: 'g3',
        name: '小花朵组',
        children: ['child-9', 'child-10', 'child-11', 'child-12'],
        color: '#F59E0B',
      },
    ],
    observationCount: 24,
    parentParticipationRate: 85,
  },
  {
    id: '2',
    title: '我们的社区',
    description:
      '带领孩子们探索周围的社区环境，认识不同的职业和社区服务，培养社会责任感。',
    status: 'active',
    progress: 30,
    startDate: '2024-03-10',
    endDate: '2024-05-01',
    classId: 'class-1',
    className: '大班一班',
    childrenCount: 28,
    activitiesCount: 15,
    completedActivities: 4,
    lastActivity: '参观消防站',
    coverImage: '/images/community-project.jpg',
    tags: ['社会认知', '职业体验', '公民教育'],
    timeline: [
      {
        id: 't6',
        title: '社区探索',
        date: '2024-03-10',
        status: 'completed',
        description: '实地走访社区，观察周围环境',
      },
      {
        id: 't7',
        title: '职业认知',
        date: '2024-03-20',
        status: 'current',
        description: '了解不同职业的工作内容',
      },
      {
        id: 't8',
        title: '角色扮演',
        date: '2024-04-05',
        status: 'upcoming',
        description: '模拟不同职业的工作场景',
      },
    ],
    groups: [
      {
        id: 'g4',
        name: '小警察组',
        children: ['child-1', 'child-2', 'child-3'],
        color: '#EF4444',
      },
      {
        id: 'g5',
        name: '小医生组',
        children: ['child-4', 'child-5', 'child-6'],
        color: '#06B6D4',
      },
    ],
    observationCount: 18,
    parentParticipationRate: 72,
  },
  {
    id: '3',
    title: '奇妙的颜色世界',
    description:
      '通过色彩实验和艺术创作，让孩子们探索颜色的奥秘，发展艺术表现力和创造力。',
    status: 'completed',
    progress: 100,
    startDate: '2024-02-01',
    endDate: '2024-02-28',
    classId: 'class-2',
    className: '中班二班',
    childrenCount: 25,
    activitiesCount: 10,
    completedActivities: 10,
    lastActivity: '彩虹艺术展示',
    coverImage: '/images/color-project.jpg',
    tags: ['艺术创作', '科学实验', '感官体验'],
    timeline: [
      {
        id: 't9',
        title: '颜色认知',
        date: '2024-02-01',
        status: 'completed',
        description: '认识基本颜色，了解颜色特性',
      },
      {
        id: 't10',
        title: '混色实验',
        date: '2024-02-10',
        status: 'completed',
        description: '探索颜色混合的奇妙变化',
      },
      {
        id: 't11',
        title: '艺术创作',
        date: '2024-02-20',
        status: 'completed',
        description: '用颜色创作美丽的艺术作品',
      },
    ],
    groups: [
      {
        id: 'g7',
        name: '红色小队',
        children: ['child-4', 'child-5'],
        color: '#EF4444',
      },
      {
        id: 'g8',
        name: '蓝色小队',
        children: ['child-6', 'child-7'],
        color: '#3B82F6',
      },
    ],
    observationCount: 30,
    parentParticipationRate: 92,
  },
  {
    id: '4',
    title: '动物朋友们',
    description: '认识不同的动物朋友，了解它们的生活习性，培养爱护动物的情感。',
    status: 'draft',
    progress: 0,
    startDate: '2024-04-01',
    endDate: '2024-05-15',
    classId: 'class-3',
    className: '小班三班',
    childrenCount: 22,
    activitiesCount: 8,
    completedActivities: 0,
    lastActivity: '',
    coverImage: '/images/animal-project.jpg',
    tags: ['动物认知', '情感教育', '自然探索'],
    timeline: [
      {
        id: 't12',
        title: '动物认识',
        date: '2024-04-01',
        status: 'upcoming',
        description: '认识常见的动物朋友',
      },
      {
        id: 't13',
        title: '习性观察',
        date: '2024-04-15',
        status: 'upcoming',
        description: '观察动物的生活习性',
      },
    ],
    groups: [
      {
        id: 'g9',
        name: '小猫组',
        children: ['child-1', 'child-2'],
        color: '#F59E0B',
      },
      {
        id: 'g10',
        name: '小狗组',
        children: ['child-3', 'child-4'],
        color: '#8B5CF6',
      },
    ],
    observationCount: 0,
    parentParticipationRate: 0,
  },
  {
    id: '5',
    title: '传统节日探秘',
    description: '通过体验传统节日活动，让孩子们了解中华文化，传承民族精神。',
    status: 'paused',
    progress: 45,
    startDate: '2024-01-15',
    endDate: '2024-03-31',
    classId: 'class-2',
    className: '中班二班',
    childrenCount: 25,
    activitiesCount: 6,
    completedActivities: 3,
    lastActivity: '制作元宵花灯',
    coverImage: '/images/festival-project.jpg',
    tags: ['文化传承', '手工制作', '节日体验'],
    timeline: [
      {
        id: 't14',
        title: '春节文化',
        date: '2024-01-15',
        status: 'completed',
        description: '了解春节的传统文化',
      },
      {
        id: 't15',
        title: '元宵制作',
        date: '2024-02-24',
        status: 'completed',
        description: '制作传统元宵花灯',
      },
      {
        id: 't16',
        title: '清明习俗',
        date: '2024-04-04',
        status: 'upcoming',
        description: '学习清明节传统习俗',
      },
    ],
    groups: [
      {
        id: 'g11',
        name: '龙年组',
        children: ['child-4', 'child-5', 'child-6'],
        color: '#DC2626',
      },
      {
        id: 'g12',
        name: '福字组',
        children: ['child-7', 'child-8', 'child-9'],
        color: '#059669',
      },
    ],
    observationCount: 15,
    parentParticipationRate: 78,
  },
];

// Mock统计数据
export const mockDashboardStats: DashboardStats = {
  activeProjects: 3,
  weeklyActivities: 8,
  pendingEvaluations: 12,
  parentParticipation: 85,
};

// Mock活动数据
export const mockActivities: Activity[] = [
  {
    id: '1',
    projectId: '1',
    title: '种子观察日记',
    description: '孩子们用放大镜观察不同种子的形状、大小和颜色',
    date: '2024-03-15',
    type: 'observation',
    photos: [
      'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?w=400&h=300&fit=crop',
    ],
    videos: [],
    notes: '孩子们对种子的形状很感兴趣，小明发现了豆子上的小眼睛',
    participants: ['child-1', 'child-2', 'child-3'],
  },
  {
    id: '2',
    projectId: '1',
    title: '种植豆芽实验',
    description: '将豆子放在湿润的纸巾上，观察发芽过程',
    date: '2024-03-18',
    type: 'experiment',
    photos: [
      'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=300&fit=crop',
    ],
    videos: [],
    notes: '孩子们很兴奋地参与种植过程，期待看到豆芽长出来',
    participants: ['child-1', 'child-4', 'child-5'],
  },
  {
    id: '3',
    projectId: '1',
    title: '豆芽生长记录',
    description: '记录豆芽每天的生长变化，测量高度',
    date: '2024-03-22',
    type: 'observation',
    photos: [
      'https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=300&fit=crop',
    ],
    videos: [],
    notes: '豆芽长得很快，孩子们每天都很期待看到新的变化',
    participants: ['child-1', 'child-2', 'child-3', 'child-4'],
  },
  {
    id: '4',
    projectId: '1',
    title: '植物绘画创作',
    description: '用画笔记录观察到的植物生长过程',
    date: '2024-03-25',
    type: 'creation',
    photos: [
      'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=300&fit=crop',
    ],
    videos: [],
    notes: '孩子们用彩色画笔画出了自己观察到的豆芽，非常生动',
    participants: ['child-2', 'child-3', 'child-5', 'child-6'],
  },
];

// Mock儿童数据
export const mockChildren: Child[] = [
  {
    id: 'child-1',
    name: '小明',
    avatar:
      'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face',
    classId: 'class-1',
    age: 5,
  },
  {
    id: 'child-2',
    name: '小红',
    avatar:
      'https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=100&h=100&fit=crop&crop=face',
    classId: 'class-1',
    age: 5,
  },
  {
    id: 'child-3',
    name: '小刚',
    avatar:
      'https://images.unsplash.com/photo-1519340333755-56e9c1d3611a?w=100&h=100&fit=crop&crop=face',
    classId: 'class-1',
    age: 6,
  },
  {
    id: 'child-4',
    name: '小丽',
    avatar:
      'https://images.unsplash.com/photo-1503454537195-1dcabb73ffb9?w=100&h=100&fit=crop&crop=face',
    classId: 'class-1',
    age: 4,
  },
  {
    id: 'child-5',
    name: '小华',
    avatar:
      'https://images.unsplash.com/photo-1554151228-14d9def656e4?w=100&h=100&fit=crop&crop=face',
    classId: 'class-1',
    age: 4,
  },
  {
    id: 'child-6',
    name: '小芳',
    avatar:
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
    classId: 'class-1',
    age: 5,
  },
  {
    id: 'child-7',
    name: '小强',
    avatar:
      'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=100&h=100&fit=crop&crop=face',
    classId: 'class-1',
    age: 5,
  },
  {
    id: 'child-8',
    name: '小美',
    avatar:
      'https://images.unsplash.com/photo-1488426862026-3ee34a7d66df?w=100&h=100&fit=crop&crop=face',
    classId: 'class-1',
    age: 6,
  },
  {
    id: 'child-9',
    name: '小杰',
    avatar:
      'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
    classId: 'class-1',
    age: 5,
  },
  {
    id: 'child-10',
    name: '小雨',
    avatar:
      'https://images.unsplash.com/photo-1494790108755-2616c9c0b8d3?w=100&h=100&fit=crop&crop=face',
    classId: 'class-1',
    age: 4,
  },
  {
    id: 'child-11',
    name: '小阳',
    avatar:
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
    classId: 'class-1',
    age: 5,
  },
  {
    id: 'child-12',
    name: '小月',
    avatar:
      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
    classId: 'class-1',
    age: 6,
  },
];

// AI主题建议Mock数据
export const mockAIThemes = [
  {
    id: '1',
    title: '春日花开',
    description: '探索春天的花朵，观察花的结构和生长过程',
    season: 'spring',
    ageGroup: '4-6岁',
    duration: '3-4周',
    keywords: ['花朵', '春天', '观察', '绘画'],
  },
  {
    id: '2',
    title: '小小建筑师',
    description: '用积木和废料搭建房屋，学习建筑结构',
    season: 'all',
    ageGroup: '3-5岁',
    duration: '2-3周',
    keywords: ['建筑', '搭建', '空间', '创造'],
  },
  {
    id: '3',
    title: '水的奇妙旅程',
    description: '探索水的三态变化，了解水循环过程',
    season: 'all',
    ageGroup: '5-6岁',
    duration: '4-5周',
    keywords: ['水', '实验', '科学', '循环'],
  },
];

// AI 生成项目的数据结构
export interface AIGeneratedProject {
  title: string;
  description: string;
  objectives: string[];
  drivingQuestions: string[];
  suggestedDuration: string;
  ageGroup: string;
  tags: string[];
}

// Mock AI 生成项目函数
export const generateProjectFromKeyword = async (
  keyword: string
): Promise<AIGeneratedProject> => {
  // 模拟 API 调用延迟
  await new Promise((resolve) => setTimeout(resolve, 2000));

  // 根据关键词生成不同的项目内容
  const projectTemplates: Record<string, AIGeneratedProject> = {
    春天: {
      title: '春天的奇妙发现',
      description:
        '通过观察春天的变化，让孩子们探索自然界的生命力，培养观察能力和对自然的热爱。',
      objectives: [
        '培养孩子们的观察能力和记录习惯',
        '了解春天植物和动物的变化规律',
        '发展语言表达和艺术创作能力',
        '增强对自然环境的保护意识',
      ],
      drivingQuestions: [
        '春天来了，我们周围发生了哪些变化？',
        '为什么有些植物会在春天开花？',
        '小动物们在春天都在做什么？',
        '我们可以用什么方式记录春天的美好？',
      ],
      suggestedDuration: '4-6周',
      ageGroup: '4-6岁',
      tags: ['自然观察', '季节变化', '生命教育', '艺术表达'],
    },
    动物: {
      title: '动物王国大探险',
      description:
        '带领孩子们走进动物的世界，了解不同动物的特征和生活习性，培养爱护动物的情感。',
      objectives: [
        '认识不同动物的外形特征和生活习性',
        '了解动物与环境的关系',
        '培养爱护动物和保护环境的意识',
        '发展分类和比较的思维能力',
      ],
      drivingQuestions: [
        '动物们都住在哪里？为什么？',
        '不同的动物有什么不同的本领？',
        '动物宝宝和动物妈妈有什么相同和不同？',
        '我们应该怎样保护动物朋友？',
      ],
      suggestedDuration: '3-5周',
      ageGroup: '3-6岁',
      tags: ['动物认知', '生命教育', '环保意识', '分类思维'],
    },
    颜色: {
      title: '彩虹色彩实验室',
      description:
        '通过丰富的色彩实验和艺术创作，让孩子们探索颜色的奥秘，发展艺术感知和创造能力。',
      objectives: [
        '认识基本颜色和颜色混合的规律',
        '发展艺术创作和审美能力',
        '培养科学探究的兴趣和方法',
        '提升观察力和表达能力',
      ],
      drivingQuestions: [
        '红色和黄色混合会变成什么颜色？',
        '我们身边有哪些美丽的颜色？',
        '不同的颜色会给我们什么样的感觉？',
        '我们可以用颜色创作出什么作品？',
      ],
      suggestedDuration: '2-4周',
      ageGroup: '3-5岁',
      tags: ['色彩认知', '艺术创作', '科学实验', '感官体验'],
    },
    水: {
      title: '水的神奇之旅',
      description:
        '探索水的各种形态和用途，通过实验了解水的特性，培养科学探究精神。',
      objectives: [
        '了解水的基本特性和三态变化',
        '认识水在生活中的重要作用',
        '培养科学实验的兴趣和能力',
        '增强节约用水的环保意识',
      ],
      drivingQuestions: [
        '水可以变成什么样子？',
        '为什么冰会融化成水？',
        '植物和动物为什么需要水？',
        '我们应该怎样节约用水？',
      ],
      suggestedDuration: '3-4周',
      ageGroup: '4-6岁',
      tags: ['科学探究', '物质变化', '环保教育', '实验操作'],
    },
  };

  // 默认模板（当关键词不匹配时）
  const defaultTemplate: AIGeneratedProject = {
    title: `探索"${keyword}"的奥秘`,
    description: `通过多元化的活动和探索，让孩子们深入了解"${keyword}"，培养好奇心和探究能力。`,
    objectives: [
      `认识和了解${keyword}的基本特征`,
      '培养观察和探究的能力',
      '发展语言表达和交流能力',
      '增强对周围世界的好奇心',
    ],
    drivingQuestions: [
      `${keyword}是什么样的？`,
      `我们在哪里可以找到${keyword}？`,
      `${keyword}有什么用处？`,
      `关于${keyword}，我们还想知道什么？`,
    ],
    suggestedDuration: '3-4周',
    ageGroup: '3-6岁',
    tags: ['探索发现', '认知发展', '语言表达', '好奇心培养'],
  };

  // 查找匹配的模板
  const matchedTemplate = Object.keys(projectTemplates).find(
    (key) => keyword.includes(key) || key.includes(keyword)
  );

  return matchedTemplate ? projectTemplates[matchedTemplate] : defaultTemplate;
};
