# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
next-env.d.ts
# Database
*.db

# testing
/coverage

# storybook
storybook-static

# playwright
/test-results/
/playwright-report/
/playwright/.cache/

# next.js
/.next
/out

# next-sitemap
public/robots.txt
public/sitemap.xml
public/sitemap-*.xml
public/pdf

# cache
.swc/

# production
/build

# misc
.DS_Store
*.pem
Thumbs.db

# debug
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# local folder
local

# vercel
.vercel
/.idea
pnpm-lock.yaml
/.yarn


# START Ruler Generated Files
*.bak
.kilocode/mcp.json
.kilocode/mcp.json.bak
.kilocode/rules/ruler_kilocode_instructions.md
.kilocode/rules/ruler_kilocode_instructions.md.bak
.mcp.json
.mcp.json.bak
CLAUDE.md
CLAUDE.md.bak
# END Ruler Generated Files
